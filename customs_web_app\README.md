# 🌐 نظام إدارة موظفي الجمارك الجزائرية - تطبيق ويب
## Algerian Customs Personnel Management System - Web Application

### 🚀 نظرة عامة

تطبيق ويب متطور وعصري لإدارة موظفي الجمارك الجزائرية، مطور بتقنيات حديثة مع واجهة مستخدم جميلة ومتجاوبة.

### ✨ الميزات الرئيسية

#### 🎨 واجهة مستخدم عصرية
- **تصميم متجاوب**: يعمل على جميع الأجهزة (كمبيوتر، تابلت، هاتف)
- **واجهة عربية كاملة**: دعم كامل للغة العربية مع تخطيط RTL
- **تصميم Material Design**: واجهة حديثة وجميلة
- **رسوم بيانية تفاعلية**: إحصائيات مرئية باستخدام Chart.js
- **تأثيرات بصرية**: انتقالات سلسة وتأثيرات جذابة

#### 🔐 نظام أمان متقدم
- **مصادقة متعددة المستويات**: تسجيل دخول آمن
- **إدارة الأدوار والصلاحيات**: تحكم دقيق في الوصول
- **حماية CSRF**: حماية من الهجمات الأمنية
- **تشفير كلمات المرور**: باستخدام bcrypt
- **جلسات آمنة**: إدارة متقدمة للجلسات

#### 👥 إدارة شاملة للموظفين
- **البيانات الأساسية**: معلومات شخصية كاملة
- **البيانات المهنية**: أسلاك، رتب، وظائف، مديريات
- **الملفات الفرعية**: شهادات، تكوين، لغات، عطل
- **إدارة الصور**: رفع وعرض صور الموظفين
- **التحقق الذكي**: تحقق من صحة البيانات الجزائرية

#### 📊 لوحة تحكم متقدمة
- **إحصائيات فورية**: أرقام محدثة في الوقت الفعلي
- **رسوم بيانية تفاعلية**: مخططات دائرية وأعمدة
- **تنبيهات ذكية**: إشعارات للأحداث المهمة
- **ملخص سريع**: نظرة عامة على النظام

#### 📈 نظام تقارير متطور
- **تقارير ديناميكية**: إنشاء تقارير مخصصة
- **تصدير متعدد الصيغ**: PDF, Excel, CSV
- **فلترة متقدمة**: بحث وفلترة دقيقة
- **طباعة محسنة**: تنسيق مثالي للطباعة

#### ⚙️ إدارة إعدادات مرنة
- **البيانات المرجعية**: ولايات، أسلاك، مديريات
- **استيراد/تصدير**: من وإلى Excel
- **إعدادات النظام**: تخصيص شامل
- **نسخ احتياطية**: حماية البيانات

### 🛠️ التقنيات المستخدمة

#### Backend (الخادم)
- **Flask 3.0**: إطار عمل Python متقدم
- **SQLAlchemy 2.0**: ORM حديث وقوي
- **PostgreSQL**: قاعدة بيانات متقدمة
- **Redis**: تخزين مؤقت سريع
- **Flask-Login**: إدارة المصادقة
- **Flask-WTF**: نماذج آمنة
- **Flask-Mail**: إرسال الإيميلات
- **Celery**: المهام الخلفية

#### Frontend (الواجهة)
- **Bootstrap 5**: تصميم متجاوب وحديث
- **JavaScript ES6+**: تفاعل متقدم
- **Chart.js**: رسوم بيانية جميلة
- **DataTables**: جداول تفاعلية
- **SweetAlert2**: تنبيهات أنيقة
- **Font Awesome**: أيقونات احترافية
- **jQuery**: تفاعل سلس

#### قاعدة البيانات
- **PostgreSQL**: للإنتاج
- **SQLite**: للتطوير
- **Redis**: للتخزين المؤقت
- **Flask-Migrate**: إدارة التحديثات

### 📦 التثبيت والتشغيل

#### المتطلبات الأساسية
```bash
Python 3.8+
PostgreSQL 12+
Redis 6+
Node.js (اختياري للتطوير)
```

#### 1. تحميل المشروع
```bash
git clone [repository-url]
cd customs_web_app
```

#### 2. إنشاء البيئة الافتراضية
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate
```

#### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### 4. إعداد متغيرات البيئة
```bash
cp .env.example .env
# قم بتعديل ملف .env حسب إعداداتك
```

#### 5. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
flask db init
flask db migrate -m "Initial migration"
flask db upgrade

# إدراج البيانات الأولية
flask init-db
flask seed-db
```

#### 6. تشغيل التطبيق
```bash
# للتطوير
flask run

# أو
python app.py

# للإنتاج
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### 🔧 الإعداد المتقدم

#### إعداد PostgreSQL
```sql
CREATE DATABASE customs_db;
CREATE USER customs_user WITH PASSWORD 'customs_pass';
GRANT ALL PRIVILEGES ON DATABASE customs_db TO customs_user;
```

#### إعداد Redis
```bash
# Ubuntu/Debian
sudo apt install redis-server
sudo systemctl start redis-server

# CentOS/RHEL
sudo yum install redis
sudo systemctl start redis
```

#### إعداد Nginx (للإنتاج)
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /static {
        alias /path/to/customs_web_app/app/static;
    }
}
```

### 👤 المستخدم الافتراضي

بعد تشغيل `flask seed-db`:
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- **البريد الإلكتروني**: <EMAIL>

⚠️ **مهم**: قم بتغيير كلمة المرور فوراً بعد أول تسجيل دخول!

### 📱 الاستخدام

#### 1. تسجيل الدخول
- انتقل إلى `http://localhost:5000`
- استخدم بيانات المستخدم الافتراضي
- ستنتقل إلى لوحة التحكم

#### 2. إدارة الموظفين
- انقر على "إدارة الموظفين"
- أضف موظف جديد أو عدل موظف موجود
- استخدم البحث والفلاتر للعثور على الموظفين

#### 3. عرض التقارير
- انتقل إلى "التقارير"
- اختر نوع التقرير المطلوب
- قم بتصدير التقرير بالصيغة المناسبة

#### 4. إدارة الإعدادات
- انقر على "الإعدادات"
- أضف أو عدل البيانات المرجعية
- استورد البيانات من Excel

### 🎯 الميزات المتقدمة

#### API RESTful
```bash
# الحصول على قائمة الموظفين
GET /api/employees

# إضافة موظف جديد
POST /api/employees

# تحديث موظف
PUT /api/employees/{id}

# حذف موظف
DELETE /api/employees/{id}
```

#### البحث المتقدم
- بحث نصي في جميع الحقول
- فلترة حسب المديرية، السلك، الحالة
- ترتيب النتائج
- تصدير نتائج البحث

#### الإشعارات
- إشعارات فورية للأحداث
- تنبيهات أعياد الميلاد
- تذكيرات انتهاء العقود
- إشعارات العطل المعلقة

### 🔒 الأمان

#### حماية البيانات
- تشفير كلمات المرور
- حماية من SQL Injection
- حماية من XSS
- حماية CSRF
- جلسات آمنة

#### النسخ الاحتياطية
```bash
# نسخة احتياطية من قاعدة البيانات
pg_dump customs_db > backup.sql

# استعادة النسخة الاحتياطية
psql customs_db < backup.sql
```

### 📊 الأداء

#### تحسينات الأداء
- تخزين مؤقت مع Redis
- ضغط الاستجابات
- تحسين الاستعلامات
- تحميل كسول للبيانات

#### المراقبة
- سجلات مفصلة
- مراقبة الأداء
- تتبع الأخطاء
- إحصائيات الاستخدام

### 🚀 النشر

#### Docker
```dockerfile
FROM python:3.9
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

#### Docker Compose
```yaml
version: '3.8'
services:
  web:
    build: .
    ports:
      - "5000:5000"
    depends_on:
      - db
      - redis
  
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: customs_db
      POSTGRES_USER: customs_user
      POSTGRES_PASSWORD: customs_pass
  
  redis:
    image: redis:6
```

### 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch جديد
3. إجراء التغييرات
4. إرسال Pull Request

### 📄 الترخيص

هذا المشروع مطور خصيصاً للجمارك الجزائرية.
جميع الحقوق محفوظة © 2025

### 📞 الدعم

للدعم التقني والاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +213 XXX XXX XXX

---

## 🎉 تطبيق ويب متطور وجاهز للاستخدام!

هذا التطبيق يوفر حلاً شاملاً ومتطوراً لإدارة موظفي الجمارك الجزائرية بواجهة عصرية وميزات متقدمة.
