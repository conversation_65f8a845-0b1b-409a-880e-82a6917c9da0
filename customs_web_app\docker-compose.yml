# Docker Compose لتطبيق الويب
# Docker Compose for Web Application

version: '3.8'

services:
  # تطبيق الويب
  web:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_CONFIG=production
      - DATABASE_URL=**********************************************/customs_db
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ./app/static/uploads:/app/app/static/uploads
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - customs_network

  # قاعدة البيانات PostgreSQL
  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=customs_db
      - POSTGRES_USER=customs_user
      - POSTGRES_PASSWORD=customs_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - customs_network

  # Redis للتخزين المؤقت
  redis:
    image: redis:6-alpine
    restart: unless-stopped
    networks:
      - customs_network

  # Nginx كخادم ويب عكسي
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./app/static:/var/www/static
    depends_on:
      - web
    restart: unless-stopped
    networks:
      - customs_network

volumes:
  postgres_data:

networks:
  customs_network:
    driver: bridge
