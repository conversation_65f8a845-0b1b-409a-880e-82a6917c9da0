# PowerShell script to run the Customs Management System
# ملف PowerShell لتشغيل برنامج تسيير الجمارك

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "برنامج تسيير مستخدمي الجمارك الجزائرية" -ForegroundColor Green
Write-Host "Algerian Customs Personnel Management System" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# التحقق من وجود Python
Write-Host "التحقق من وجود Python..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ تم العثور على Python: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    Write-Host "❌ Python غير مثبت على النظام" -ForegroundColor Red
    Write-Host "يرجى تثبيت Python 3.8 أو أحدث من: https://www.python.org/downloads/" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# التحقق من tkinter
Write-Host ""
Write-Host "اختبار tkinter..." -ForegroundColor Yellow
try {
    python -c "import tkinter; print('tkinter OK')" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ tkinter متوفر" -ForegroundColor Green
    } else {
        throw "tkinter not available"
    }
} catch {
    Write-Host "❌ tkinter غير متوفر" -ForegroundColor Red
    Write-Host "يرجى إعادة تثبيت Python مع تحديد 'tcl/tk and IDLE'" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# تثبيت المتطلبات الأساسية
Write-Host ""
Write-Host "التحقق من المتطلبات..." -ForegroundColor Yellow

$requirements = @("sqlalchemy", "PIL", "openpyxl")
foreach ($req in $requirements) {
    try {
        python -c "import $req" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ $req متوفر" -ForegroundColor Green
        } else {
            Write-Host "تثبيت $req..." -ForegroundColor Yellow
            if ($req -eq "PIL") {
                pip install Pillow
            } else {
                pip install $req
            }
        }
    } catch {
        Write-Host "⚠️ مشكلة في $req" -ForegroundColor Yellow
    }
}

# تشغيل اختبار بسيط
Write-Host ""
Write-Host "تشغيل اختبار بسيط..." -ForegroundColor Yellow
try {
    python basic_test.py
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ الاختبار البسيط نجح" -ForegroundColor Green
    } else {
        throw "Basic test failed"
    }
} catch {
    Write-Host "❌ فشل الاختبار البسيط" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# تشغيل البرنامج الرئيسي
Write-Host ""
Write-Host "تشغيل البرنامج الرئيسي..." -ForegroundColor Cyan
Write-Host "🚀 البرنامج جاهز للاستخدام!" -ForegroundColor Green

try {
    python main.py
} catch {
    Write-Host ""
    Write-Host "❌ حدث خطأ في تشغيل البرنامج" -ForegroundColor Red
    Write-Host "جرب تشغيل: python run.py" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "تم إغلاق البرنامج" -ForegroundColor Cyan
Read-Host "اضغط Enter للخروج"
