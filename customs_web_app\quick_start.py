#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لتطبيق الويب
Quick start for web application
"""

import os
import sys

def main():
    """تشغيل سريع للتطبيق"""
    print("🚀 تشغيل سريع لتطبيق الويب")
    print("=" * 50)
    
    # تعيين متغيرات البيئة
    os.environ['FLASK_CONFIG'] = 'development'
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = '1'
    
    try:
        # استيراد التطبيق
        from app import create_app, db
        from app.models import User
        
        # إنشاء التطبيق
        app = create_app('development')
        
        with app.app_context():
            # إنشاء قاعدة البيانات
            db.create_all()
            
            # إنشاء مستخدم مدير إذا لم يكن موجوداً
            if not User.query.filter_by(username='admin').first():
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    first_name='مدير',
                    last_name='النظام',
                    is_admin=True
                )
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()
                print("✅ تم إنشاء المستخدم المدير")
        
        print("✅ تم إعداد التطبيق بنجاح")
        print()
        print("=" * 50)
        print("🌐 تطبيق الويب لتسيير مستخدمي الجمارك")
        print("=" * 50)
        print("📍 الرابط: http://localhost:5000")
        print("👤 اسم المستخدم: admin")
        print("🔑 كلمة المرور: admin123")
        print("=" * 50)
        print("🚀 جاري تشغيل التطبيق...")
        
        # تشغيل التطبيق
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("يرجى تثبيت المتطلبات: pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
