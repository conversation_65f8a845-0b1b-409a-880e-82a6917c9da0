# 🚀 دليل التشغيل السريع
## Quick Launch Guide

### 🌟 طرق التشغيل المتاحة

#### 1. التشغيل السريع (الأسهل)
```bash
python quick_start.py
```

#### 2. التشغيل على Windows
```cmd
start_web_app.bat
```

#### 3. التشغيل على Linux/macOS
```bash
./start_web_app.sh
```

#### 4. التشغيل المتقدم
```bash
python run_web_app.py
```

#### 5. التشغيل المباشر
```bash
python app.py
```

### 🔧 متطلبات التشغيل

- **Python 3.8+**
- **pip** (مدير الحزم)
- **اتصال بالإنترنت** (لتحميل المتطلبات)

### 📋 خطوات التشغيل السريع

1. **تحميل المشروع**
   ```bash
   cd customs_web_app
   ```

2. **تشغيل التطبيق**
   ```bash
   python quick_start.py
   ```

3. **فتح المتصفح**
   - انتقل إلى: `http://localhost:5000`
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

### 🎯 ما يحدث تلقائياً

✅ **تثبيت المتطلبات** - تلقائياً
✅ **إنشاء قاعدة البيانات** - SQLite محلية
✅ **إنشاء المستخدم المدير** - admin/admin123
✅ **تشغيل الخادم** - localhost:5000
✅ **واجهة جاهزة** - فوراً

### 🌐 الوصول للتطبيق

بعد التشغيل الناجح:

- **الرابط المحلي**: http://localhost:5000
- **الرابط الشبكي**: http://[your-ip]:5000

### 👤 بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- **البريد الإلكتروني**: <EMAIL>

⚠️ **مهم**: غير كلمة المرور فوراً بعد أول تسجيل دخول!

### 🛠️ استكشاف الأخطاء

#### مشكلة: Python غير موجود
```bash
# تثبيت Python
# Windows: تحميل من python.org
# Ubuntu: sudo apt install python3
# CentOS: sudo yum install python3
```

#### مشكلة: pip غير موجود
```bash
# تثبيت pip
python -m ensurepip --upgrade
```

#### مشكلة: فشل تثبيت المتطلبات
```bash
# تحديث pip
python -m pip install --upgrade pip

# تثبيت يدوي
pip install flask flask-sqlalchemy flask-login
```

#### مشكلة: المنفذ 5000 مستخدم
```bash
# تغيير المنفذ في app.py
app.run(port=5001)
```

### 🐳 التشغيل باستخدام Docker

```bash
# بناء الصورة
docker build -t customs-web-app .

# تشغيل الحاوية
docker run -p 5000:5000 customs-web-app

# أو استخدام docker-compose
docker-compose up
```

### 🔄 إعادة التشغيل

لإعادة تشغيل التطبيق:
1. أوقف التطبيق: `Ctrl+C`
2. شغل مرة أخرى: `python quick_start.py`

### 📱 الوصول من الهاتف

1. تأكد أن الكمبيوتر والهاتف على نفس الشبكة
2. اعرف IP الكمبيوتر: `ipconfig` (Windows) أو `ifconfig` (Linux)
3. افتح في الهاتف: `http://[computer-ip]:5000`

### 🎉 التطبيق جاهز!

بعد التشغيل الناجح ستحصل على:

✨ **واجهة عصرية** - تصميم Bootstrap 5
📊 **لوحة تحكم** - إحصائيات ورسوم بيانية
👥 **إدارة موظفين** - شاملة ومتقدمة
📈 **تقارير** - متنوعة وقابلة للتصدير
⚙️ **إعدادات** - مرنة وشاملة
🔐 **أمان** - متعدد المستويات

### 🆘 الحصول على المساعدة

إذا واجهت أي مشاكل:
1. تحقق من رسائل الخطأ
2. راجع ملف `README.md`
3. تأكد من تثبيت Python 3.8+
4. جرب التشغيل كمدير (Windows)

---

## 🚀 ابدأ الآن!

```bash
python quick_start.py
```

**التطبيق سيكون جاهزاً خلال دقائق!** 🎯
