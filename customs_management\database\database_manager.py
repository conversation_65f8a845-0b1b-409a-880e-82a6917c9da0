# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
Database Manager for Customs Personnel Management System
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
import os
from typing import Optional, List, Any

from .models import Base, Employee, Wilaya, Commune, Corps, Rank, Directorate, Service, Position, Assignment
from .models import Certificate, Training, Language, EmployeeLanguage, Transfer, AnnualLeave, SickLeave, OtherLeave
from .models import Deposit, Suspension, Death, Delegation, Resignation, ExternalTransfer, RankPromotion, GradePromotion
from .models import Spouse, Child, Dependent, PenaltyType, Penalty, Reward, Institution
from utils.config import Config

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self):
        """تهيئة مدير قاعدة البيانات"""
        # إنشاء المجلدات المطلوبة
        Config.create_directories()
        
        # إنشاء محرك قاعدة البيانات
        self.engine = create_engine(
            Config.DATABASE_URL,
            echo=False,  # تعيين True لعرض استعلامات SQL
            pool_pre_ping=True
        )
        
        # إنشاء جلسة قاعدة البيانات
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            Base.metadata.create_all(bind=self.engine)
            print("تم إنشاء جداول قاعدة البيانات بنجاح")
            
            # إدراج البيانات الأساسية
            self._insert_initial_data()
            
        except SQLAlchemyError as e:
            print(f"خطأ في إنشاء جداول قاعدة البيانات: {e}")
            raise
    
    def get_session(self) -> Session:
        """الحصول على جلسة قاعدة البيانات"""
        return self.SessionLocal()
    
    def _insert_initial_data(self):
        """إدراج البيانات الأساسية"""
        session = self.get_session()
        try:
            # التحقق من وجود البيانات
            if session.query(Wilaya).count() == 0:
                self._insert_wilayas_and_communes(session)
            
            if session.query(Corps).count() == 0:
                self._insert_corps_and_ranks(session)
            
            if session.query(Language).count() == 0:
                self._insert_languages(session)
            
            if session.query(PenaltyType).count() == 0:
                self._insert_penalty_types(session)
            
            session.commit()
            print("تم إدراج البيانات الأساسية بنجاح")
            
        except SQLAlchemyError as e:
            session.rollback()
            print(f"خطأ في إدراج البيانات الأساسية: {e}")
        finally:
            session.close()
    
    def _insert_wilayas_and_communes(self, session: Session):
        """إدراج الولايات والبلديات"""
        # بيانات الولايات الجزائرية
        wilayas_data = [
            ("01", "أدرار", "Adrar"),
            ("02", "الشلف", "Chlef"),
            ("03", "الأغواط", "Laghouat"),
            ("04", "أم البواقي", "Oum El Bouaghi"),
            ("05", "باتنة", "Batna"),
            ("06", "بجاية", "Béjaïa"),
            ("07", "بسكرة", "Biskra"),
            ("08", "بشار", "Béchar"),
            ("09", "البليدة", "Blida"),
            ("10", "البويرة", "Bouira"),
            ("11", "تمنراست", "Tamanrasset"),
            ("12", "تبسة", "Tébessa"),
            ("13", "تلمسان", "Tlemcen"),
            ("14", "تيارت", "Tiaret"),
            ("15", "تيزي وزو", "Tizi Ouzou"),
            ("16", "الجزائر", "Alger"),
            ("17", "الجلفة", "Djelfa"),
            ("18", "جيجل", "Jijel"),
            ("19", "سطيف", "Sétif"),
            ("20", "سعيدة", "Saïda"),
            ("21", "سكيكدة", "Skikda"),
            ("22", "سيدي بلعباس", "Sidi Bel Abbès"),
            ("23", "عنابة", "Annaba"),
            ("24", "قالمة", "Guelma"),
            ("25", "قسنطينة", "Constantine"),
            ("26", "المدية", "Médéa"),
            ("27", "مستغانم", "Mostaganem"),
            ("28", "المسيلة", "M'Sila"),
            ("29", "معسكر", "Mascara"),
            ("30", "ورقلة", "Ouargla"),
            ("31", "وهران", "Oran"),
            ("32", "البيض", "El Bayadh"),
            ("33", "إليزي", "Illizi"),
            ("34", "برج بوعريريج", "Bordj Bou Arréridj"),
            ("35", "بومرداس", "Boumerdès"),
            ("36", "الطارف", "El Tarf"),
            ("37", "تندوف", "Tindouf"),
            ("38", "تيسمسيلت", "Tissemsilt"),
            ("39", "الوادي", "El Oued"),
            ("40", "خنشلة", "Khenchela"),
            ("41", "سوق أهراس", "Souk Ahras"),
            ("42", "تيبازة", "Tipaza"),
            ("43", "ميلة", "Mila"),
            ("44", "عين الدفلى", "Aïn Defla"),
            ("45", "النعامة", "Naâma"),
            ("46", "عين تموشنت", "Aïn Témouchent"),
            ("47", "غرداية", "Ghardaïa"),
            ("48", "غليزان", "Relizane"),
            ("49", "تيميمون", "Timimoun"),
            ("50", "برج باجي مختار", "Bordj Badji Mokhtar"),
            ("51", "أولاد جلال", "Ouled Djellal"),
            ("52", "بني عباس", "Béni Abbès"),
            ("53", "إن صالح", "In Salah"),
            ("54", "إن قزام", "In Guezzam"),
            ("55", "توقرت", "Touggourt"),
            ("56", "جانت", "Djanet"),
            ("57", "المغير", "El M'Ghair"),
            ("58", "المنيعة", "El Meniaa")
        ]
        
        for code, name_ar, name_fr in wilayas_data:
            wilaya = Wilaya(code=code, name_ar=name_ar, name_fr=name_fr)
            session.add(wilaya)
        
        session.flush()  # للحصول على معرفات الولايات
        
        # إضافة بعض البلديات كمثال (يمكن إضافة المزيد لاحقاً)
        laghouat_wilaya = session.query(Wilaya).filter_by(code="03").first()
        if laghouat_wilaya:
            communes_data = [
                ("030101", "الأغواط", "Laghouat"),
                ("030102", "كاف الأحجار", "Kef El Ahjar"),
                ("030103", "قلتة سيدي سعد", "Gueltet Sidi Saâd"),
                ("030104", "عين ماضي", "Aïn Madhi"),
                ("030105", "تاجموت", "Tadjmout"),
                ("030106", "الحويطة", "El Houaita"),
                ("030107", "سيدي مخلوف", "Sidi Makhlouf"),
                ("030108", "حاسي الرمل", "Hassi R'Mel"),
                ("030109", "حاسي الدلاعة", "Hassi Delaa"),
                ("030110", "عين سيدي علي", "Aïn Sidi Ali"),
                ("030111", "بريدة", "Brida"),
                ("030112", "الغيشة", "El Ghicha"),
                ("030113", "فليطة", "Fleita"),
                ("030114", "قصر الحيران", "Ksar El Hirane"),
                ("030115", "سبقاق", "Sebgag"),
                ("030116", "تاويالة", "Taouiala"),
                ("030117", "أفلو", "Aflou"),
                ("030118", "الحاج المشري", "El Hadj Mechri"),
                ("030119", "وادي مرة", "Oued Morra"),
                ("030120", "وادي مزي", "Oued M'Zi"),
                ("030121", "الخنق", "El Kheneg"),
                ("030122", "قلعة بوقطب", "Gueltat Bouqtob"),
                ("030123", "بن ناصر بن شهرة", "Benacer Benchohra"),
                ("030124", "عين الإبل", "Aïn El Ibel")
            ]
            
            for code, name_ar, name_fr in communes_data:
                commune = Commune(
                    code=code,
                    name_ar=name_ar,
                    name_fr=name_fr,
                    wilaya_id=laghouat_wilaya.id
                )
                session.add(commune)
    
    def _insert_corps_and_ranks(self, session: Session):
        """إدراج الأسلاك والرتب"""
        # الأسلاك
        corps_data = [
            ("أسلاك خاصة", "Corps spéciaux"),
            ("أسلاك مشتركة", "Corps communs"),
            ("عمال مهنيين", "Ouvriers professionnels")
        ]
        
        for name_ar, name_fr in corps_data:
            corps = Corps(name_ar=name_ar, name_fr=name_fr)
            session.add(corps)
        
        session.flush()
        
        # الرتب للأسلاك الخاصة
        special_corps = session.query(Corps).filter_by(name_ar="أسلاك خاصة").first()
        if special_corps:
            ranks_data = [
                ("مفتش رئيسي للجمارك", "Inspecteur principal des douanes", 1),
                ("مفتش للجمارك", "Inspecteur des douanes", 2),
                ("مفتش مساعد للجمارك", "Inspecteur adjoint des douanes", 3),
                ("ضابط للجمارك", "Officier des douanes", 4),
                ("ضابط مساعد للجمارك", "Officier adjoint des douanes", 5),
                ("عون تحكم للجمارك", "Agent de maîtrise des douanes", 6),
                ("عون للجمارك", "Agent des douanes", 7)
            ]
            
            for name_ar, name_fr, level in ranks_data:
                rank = Rank(
                    name_ar=name_ar,
                    name_fr=name_fr,
                    corps_id=special_corps.id,
                    level=level
                )
                session.add(rank)
    
    def _insert_languages(self, session: Session):
        """إدراج اللغات"""
        languages_data = [
            ("العربية", "Arabe"),
            ("الفرنسية", "Français"),
            ("الإنجليزية", "Anglais"),
            ("الألمانية", "Allemand"),
            ("الإسبانية", "Espagnol"),
            ("الإيطالية", "Italien"),
            ("الأمازيغية", "Amazigh")
        ]
        
        for name_ar, name_fr in languages_data:
            language = Language(name_ar=name_ar, name_fr=name_fr)
            session.add(language)
    
    def _insert_penalty_types(self, session: Session):
        """إدراج أنواع العقوبات"""
        penalty_types_data = [
            (1, "إنذار"),
            (1, "توبيخ"),
            (2, "الحسم من الراتب"),
            (2, "التوقيف المؤقت"),
            (3, "التنزيل في الرتبة"),
            (3, "التسريح"),
            (4, "العزل")
        ]
        
        for degree, name_ar in penalty_types_data:
            penalty_type = PenaltyType(degree=degree, name_ar=name_ar)
            session.add(penalty_type)
    
    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if hasattr(self, 'engine'):
            self.engine.dispose()
