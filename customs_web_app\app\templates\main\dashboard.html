{% extends "base.html" %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-tachometer-alt me-3"></i>لوحة التحكم</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item active">لوحة التحكم</li>
        </ol>
    </nav>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card bg-primary">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-number">{{ stats.employees.total }}</div>
                        <div class="stats-label">إجمالي الموظفين</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card bg-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-number">{{ stats.employees.active }}</div>
                        <div class="stats-label">الموظفين النشطين</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card bg-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-number">{{ stats.leaves.total }}</div>
                        <div class="stats-label">العطل هذا العام</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card bg-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-number">{{ stats.users.total }}</div>
                        <div class="stats-label">المستخدمين</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-shield fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الرسوم البيانية -->
<div class="row mb-4">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع الموظفين حسب الحالة
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-doughnut me-2"></i>
                    توزيع الموظفين حسب الجنس
                </h5>
            </div>
            <div class="card-body">
                <canvas id="genderChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    العطل حسب الشهر
                </h5>
            </div>
            <div class="card-body">
                <canvas id="leavesChart" width="400" height="150"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    الفئات العمرية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="ageChart" width="400" height="150"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- الموظفون الجدد والأحداث القادمة -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    الموظفون الجدد
                </h5>
            </div>
            <div class="card-body">
                {% if recent_employees %}
                    <div class="list-group list-group-flush">
                        {% for employee in recent_employees %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ employee.full_name_ar }}</h6>
                                    <small class="text-muted">{{ employee.registration_number }}</small>
                                </div>
                                <small class="text-muted">{{ employee.created_at|datetime }}</small>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted text-center">لا توجد إضافات جديدة</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-check me-2"></i>
                    الأحداث القادمة
                </h5>
            </div>
            <div class="card-body">
                {% if upcoming_events %}
                    <div class="list-group list-group-flush">
                        {% for event in upcoming_events %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ event.title }}</h6>
                                    <small class="text-muted">
                                        {% if event.type == 'birthday' %}
                                            <i class="fas fa-birthday-cake me-1"></i>
                                        {% endif %}
                                        {{ event.date|date }}
                                    </small>
                                </div>
                                <span class="badge bg-primary rounded-pill">
                                    {{ (event.date - today).days }} يوم
                                </span>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted text-center">لا توجد أحداث قادمة</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- العطل المعلقة -->
{% if pending_leaves %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    العطل المعلقة الموافقة
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>نوع العطلة</th>
                                <th>تاريخ البداية</th>
                                <th>عدد الأيام</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for leave in pending_leaves %}
                                <tr>
                                    <td>{{ leave.employee.full_name_ar }}</td>
                                    <td>عطلة سنوية</td>
                                    <td>{{ leave.start_date|date }}</td>
                                    <td>{{ leave.days_count }}</td>
                                    <td>
                                        <span class="badge bg-warning">{{ leave.status }}</span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-success me-1">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تحميل بيانات الرسوم البيانية
    loadCharts();
});

function loadCharts() {
    // رسم بياني للحالة
    fetch('/api/chart-data/employees_by_status')
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('statusChart').getContext('2d');
            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: data.labels,
                    datasets: [{
                        data: data.data,
                        backgroundColor: data.backgroundColor
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        });

    // رسم بياني للجنس
    fetch('/api/chart-data/employees_by_gender')
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('genderChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: data.labels,
                    datasets: [{
                        data: data.data,
                        backgroundColor: data.backgroundColor
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        });

    // رسم بياني للعطل
    fetch('/api/chart-data/leaves_by_month')
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('leavesChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: data,
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });

    // رسم بياني للأعمار
    fetch('/api/chart-data/employees_by_age')
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('ageChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: data.labels,
                    datasets: [{
                        label: 'عدد الموظفين',
                        data: data.data,
                        backgroundColor: data.backgroundColor
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        });
}
</script>
{% endblock %}
