# -*- coding: utf-8 -*-
"""
المسارات الرئيسية
Main Routes
"""

from flask import render_template, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import func, extract
from app import db
from app.main import bp
from app.models import Employee, User, Wilaya, Corps, Directorate, AnnualLeave, SickLeave
from datetime import datetime, date

@bp.route('/')
def index():
    """الصفحة الرئيسية"""
    from flask import redirect, url_for
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    # إحصائيات عامة للصفحة الرئيسية
    total_employees = Employee.query.filter_by(is_active=True).count()
    active_employees = Employee.query.filter_by(employee_status='نشط', is_active=True).count()
    
    stats = {
        'total_employees': total_employees,
        'active_employees': active_employees,
        'inactive_employees': total_employees - active_employees
    }
    
    return render_template('main/index.html', title='الصفحة الرئيسية', stats=stats)

@bp.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم"""
    # إحصائيات شاملة
    stats = get_dashboard_stats()
    
    # الموظفون الجدد (آخر 30 يوم)
    from datetime import timedelta
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    recent_employees = Employee.query.filter(
        Employee.created_at >= thirty_days_ago,
        Employee.is_active == True
    ).order_by(Employee.created_at.desc()).limit(5).all()
    
    # العطل المعلقة
    pending_leaves = AnnualLeave.query.filter_by(status='مطلوبة').limit(5).all()
    
    # الأحداث القادمة (أعياد ميلاد، انتهاء عقود، إلخ)
    upcoming_events = get_upcoming_events()
    
    return render_template('main/dashboard.html', 
                         title='لوحة التحكم',
                         stats=stats,
                         recent_employees=recent_employees,
                         pending_leaves=pending_leaves,
                         upcoming_events=upcoming_events)

@bp.route('/api/dashboard-stats')
@login_required
def api_dashboard_stats():
    """API للحصول على إحصائيات لوحة التحكم"""
    stats = get_dashboard_stats()
    return jsonify(stats)

@bp.route('/api/chart-data/<chart_type>')
@login_required
def api_chart_data(chart_type):
    """API لبيانات الرسوم البيانية"""
    if chart_type == 'employees_by_status':
        data = get_employees_by_status_chart()
    elif chart_type == 'employees_by_gender':
        data = get_employees_by_gender_chart()
    elif chart_type == 'employees_by_corps':
        data = get_employees_by_corps_chart()
    elif chart_type == 'employees_by_age':
        data = get_employees_by_age_chart()
    elif chart_type == 'leaves_by_month':
        data = get_leaves_by_month_chart()
    else:
        data = {'error': 'نوع الرسم البياني غير مدعوم'}
    
    return jsonify(data)

def get_dashboard_stats():
    """الحصول على إحصائيات لوحة التحكم"""
    # إحصائيات الموظفين
    total_employees = Employee.query.filter_by(is_active=True).count()
    active_employees = Employee.query.filter_by(employee_status='نشط', is_active=True).count()
    male_employees = Employee.query.filter_by(gender='ذكر', is_active=True).count()
    female_employees = Employee.query.filter_by(gender='أنثى', is_active=True).count()
    
    # إحصائيات العطل
    current_year = datetime.now().year
    annual_leaves_count = AnnualLeave.query.filter_by(leave_year=current_year).count()
    sick_leaves_count = SickLeave.query.filter(
        extract('year', SickLeave.start_date) == current_year
    ).count()
    
    # إحصائيات المستخدمين
    total_users = User.query.count()
    active_users = User.query.filter_by(is_active=True).count()
    
    return {
        'employees': {
            'total': total_employees,
            'active': active_employees,
            'inactive': total_employees - active_employees,
            'male': male_employees,
            'female': female_employees,
            'male_percentage': round((male_employees / total_employees * 100) if total_employees > 0 else 0, 1),
            'female_percentage': round((female_employees / total_employees * 100) if total_employees > 0 else 0, 1)
        },
        'leaves': {
            'annual': annual_leaves_count,
            'sick': sick_leaves_count,
            'total': annual_leaves_count + sick_leaves_count
        },
        'users': {
            'total': total_users,
            'active': active_users,
            'inactive': total_users - active_users
        }
    }

def get_employees_by_status_chart():
    """بيانات رسم بياني للموظفين حسب الحالة"""
    statuses = db.session.query(
        Employee.employee_status,
        func.count(Employee.id).label('count')
    ).filter_by(is_active=True).group_by(Employee.employee_status).all()
    
    return {
        'labels': [status[0] for status in statuses],
        'data': [status[1] for status in statuses],
        'backgroundColor': [
            '#28a745',  # نشط - أخضر
            '#ffc107',  # تحويل - أصفر
            '#dc3545',  # موقف - أحمر
            '#6c757d',  # استيداع - رمادي
            '#17a2b8',  # منتدب - أزرق فاتح
            '#343a40',  # متوفي - أسود
            '#fd7e14',  # مفصول - برتقالي
            '#6f42c1'   # مستقيل - بنفسجي
        ]
    }

def get_employees_by_gender_chart():
    """بيانات رسم بياني للموظفين حسب الجنس"""
    genders = db.session.query(
        Employee.gender,
        func.count(Employee.id).label('count')
    ).filter_by(is_active=True).group_by(Employee.gender).all()
    
    return {
        'labels': [gender[0] for gender in genders],
        'data': [gender[1] for gender in genders],
        'backgroundColor': ['#007bff', '#e83e8c']  # أزرق للذكور، وردي للإناث
    }

def get_employees_by_corps_chart():
    """بيانات رسم بياني للموظفين حسب السلك"""
    corps_data = db.session.query(
        Corps.name_ar,
        func.count(Employee.id).label('count')
    ).join(Employee).filter(Employee.is_active == True).group_by(Corps.name_ar).all()
    
    return {
        'labels': [corps[0] for corps in corps_data],
        'data': [corps[1] for corps in corps_data],
        'backgroundColor': ['#28a745', '#ffc107', '#dc3545', '#17a2b8', '#6c757d']
    }

def get_employees_by_age_chart():
    """بيانات رسم بياني للموظفين حسب الفئة العمرية"""
    current_year = date.today().year
    
    # حساب الفئات العمرية
    age_groups = {
        '20-30': 0,
        '31-40': 0,
        '41-50': 0,
        '51-60': 0,
        '60+': 0
    }
    
    employees = Employee.query.filter_by(is_active=True).all()
    for emp in employees:
        if emp.birth_date:
            age = current_year - emp.birth_date.year
            if 20 <= age <= 30:
                age_groups['20-30'] += 1
            elif 31 <= age <= 40:
                age_groups['31-40'] += 1
            elif 41 <= age <= 50:
                age_groups['41-50'] += 1
            elif 51 <= age <= 60:
                age_groups['51-60'] += 1
            elif age > 60:
                age_groups['60+'] += 1
    
    return {
        'labels': list(age_groups.keys()),
        'data': list(age_groups.values()),
        'backgroundColor': ['#007bff', '#28a745', '#ffc107', '#fd7e14', '#dc3545']
    }

def get_leaves_by_month_chart():
    """بيانات رسم بياني للعطل حسب الشهر"""
    current_year = datetime.now().year
    months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
              'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
    
    # العطل السنوية
    annual_data = [0] * 12
    annual_leaves = db.session.query(
        extract('month', AnnualLeave.start_date).label('month'),
        func.count(AnnualLeave.id).label('count')
    ).filter(extract('year', AnnualLeave.start_date) == current_year).group_by('month').all()
    
    for leave in annual_leaves:
        annual_data[int(leave.month) - 1] = leave.count
    
    # العطل المرضية
    sick_data = [0] * 12
    sick_leaves = db.session.query(
        extract('month', SickLeave.start_date).label('month'),
        func.count(SickLeave.id).label('count')
    ).filter(extract('year', SickLeave.start_date) == current_year).group_by('month').all()
    
    for leave in sick_leaves:
        sick_data[int(leave.month) - 1] = leave.count
    
    return {
        'labels': months,
        'datasets': [
            {
                'label': 'العطل السنوية',
                'data': annual_data,
                'backgroundColor': 'rgba(40, 167, 69, 0.8)',
                'borderColor': 'rgba(40, 167, 69, 1)',
                'borderWidth': 1
            },
            {
                'label': 'العطل المرضية',
                'data': sick_data,
                'backgroundColor': 'rgba(220, 53, 69, 0.8)',
                'borderColor': 'rgba(220, 53, 69, 1)',
                'borderWidth': 1
            }
        ]
    }

def get_upcoming_events():
    """الحصول على الأحداث القادمة"""
    events = []
    
    # أعياد الميلاد القادمة (خلال 30 يوم)
    from datetime import timedelta
    today = date.today()
    next_month = today + timedelta(days=30)
    
    # البحث عن أعياد الميلاد
    employees = Employee.query.filter_by(is_active=True).all()
    for emp in employees:
        if emp.birth_date:
            # تحويل تاريخ الميلاد إلى السنة الحالية
            birthday_this_year = emp.birth_date.replace(year=today.year)
            if birthday_this_year < today:
                birthday_this_year = birthday_this_year.replace(year=today.year + 1)
            
            if today <= birthday_this_year <= next_month:
                events.append({
                    'type': 'birthday',
                    'title': f'عيد ميلاد {emp.full_name_ar}',
                    'date': birthday_this_year,
                    'employee': emp
                })
    
    # ترتيب الأحداث حسب التاريخ
    events.sort(key=lambda x: x['date'])
    
    return events[:10]  # أول 10 أحداث
