/* ملف CSS مخصص لنظام إدارة موظفي الجمارك */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* المتغيرات */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 10px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

/* الإعدادات العامة */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans Arabic', sans-serif;
    background-color: var(--light-color);
    line-height: 1.6;
}

/* تحسينات الطباعة */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: var(--dark-color);
}

/* الأزرار المخصصة */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: var(--transition);
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-bottom: none;
    padding: 1rem 1.5rem;
}

.card-header h5 {
    margin: 0;
    font-weight: 600;
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.1);
    transform: scale(1.01);
}

/* النماذج */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
    padding: 0.75rem 1rem;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border-left: 4px solid;
}

.alert-success {
    border-left-color: var(--success-color);
    background-color: rgba(40, 167, 69, 0.1);
}

.alert-danger {
    border-left-color: var(--danger-color);
    background-color: rgba(220, 53, 69, 0.1);
}

.alert-warning {
    border-left-color: var(--warning-color);
    background-color: rgba(255, 193, 7, 0.1);
}

.alert-info {
    border-left-color: var(--info-color);
    background-color: rgba(23, 162, 184, 0.1);
}

/* الشريط الجانبي */
.sidebar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 1rem 1.5rem;
    margin: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(-5px);
}

.sidebar .nav-link i {
    margin-left: 0.75rem;
    width: 20px;
    text-align: center;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 1;
}

.stats-card .stats-label {
    font-size: 1rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

/* رأس الصفحة */
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.page-header h1 {
    margin: 0;
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0.5rem 0 0 0;
    position: relative;
    z-index: 1;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: white;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        right: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1050;
        transition: right 0.3s ease;
    }
    
    .sidebar.show {
        right: 0;
    }
    
    .stats-card .stats-number {
        font-size: 2rem;
    }
    
    .page-header {
        padding: 1.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* تأثيرات التحميل */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3rem;
}

/* تحسينات DataTables */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin: 1rem 0;
}

.dataTables_wrapper .dataTables_filter input {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 0.5rem 1rem;
    margin-right: 0.5rem;
}

/* تحسينات الرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* تأثيرات الانتقال */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* تحسينات الطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .page-header .breadcrumb {
        display: none !important;
    }
    
    .main-content {
        margin: 0;
        box-shadow: none;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .page-header {
        background: white !important;
        color: black !important;
        border-bottom: 2px solid #ddd;
    }
}

/* تحسينات إضافية */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) 1;
}

.shadow-custom {
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
}

/* تحسينات النماذج المتقدمة */
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* تحسينات القوائم المنسدلة */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: var(--border-radius);
}

.dropdown-item:hover {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
}
