# -*- coding: utf-8 -*-
"""
مسارات المصادقة والتسجيل
Authentication and Registration Routes
"""

from flask import render_template, redirect, url_for, flash, request, session
from flask_login import login_user, logout_user, current_user, login_required
from werkzeug.urls import url_parse
from app import db
from app.auth import bp
from app.auth.forms import LoginForm, RegistrationForm, ChangePasswordForm
from app.models import User
from datetime import datetime

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        
        if user is None or not user.check_password(form.password.data):
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
            return redirect(url_for('auth.login'))
        
        if not user.is_active:
            flash('حسابك غير نشط. يرجى التواصل مع المدير', 'error')
            return redirect(url_for('auth.login'))
        
        # تحديث آخر تسجيل دخول
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        login_user(user, remember=form.remember_me.data)
        
        # إعادة التوجيه إلى الصفحة المطلوبة أو لوحة التحكم
        next_page = request.args.get('next')
        if not next_page or url_parse(next_page).netloc != '':
            next_page = url_for('main.dashboard')
        
        flash(f'مرحباً {user.full_name}!', 'success')
        return redirect(next_page)
    
    return render_template('auth/login.html', title='تسجيل الدخول', form=form)

@bp.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('main.index'))

@bp.route('/register', methods=['GET', 'POST'])
def register():
    """صفحة التسجيل (للمديرين فقط)"""
    if current_user.is_authenticated and not current_user.is_admin:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('main.dashboard'))
    
    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data
        )
        user.set_password(form.password.data)
        
        db.session.add(user)
        db.session.commit()
        
        flash(f'تم إنشاء حساب المستخدم {user.username} بنجاح', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/register.html', title='إنشاء حساب جديد', form=form)

@bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """تغيير كلمة المرور"""
    form = ChangePasswordForm()
    if form.validate_on_submit():
        if not current_user.check_password(form.old_password.data):
            flash('كلمة المرور الحالية غير صحيحة', 'error')
            return redirect(url_for('auth.change_password'))
        
        current_user.set_password(form.new_password.data)
        db.session.commit()
        
        flash('تم تغيير كلمة المرور بنجاح', 'success')
        return redirect(url_for('main.dashboard'))
    
    return render_template('auth/change_password.html', title='تغيير كلمة المرور', form=form)

@bp.route('/profile')
@login_required
def profile():
    """صفحة الملف الشخصي"""
    return render_template('auth/profile.html', title='الملف الشخصي')

@bp.route('/users')
@login_required
def users():
    """قائمة المستخدمين (للمديرين فقط)"""
    if not current_user.is_admin:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('main.dashboard'))
    
    page = request.args.get('page', 1, type=int)
    users = User.query.paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('auth/users.html', title='إدارة المستخدمين', users=users)
