#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاستيراد والتشغيل
"""

import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_basic_imports():
    """اختبار الاستيرادات الأساسية"""
    try:
        print("🔍 Testing basic imports...")

        import tkinter as tk
        print("✅ tkinter imported successfully")

        # اختبار إنشاء نافذة tkinter بسيطة
        root = tk.Tk()
        root.title("Test Window")
        root.geometry("300x200")

        label = tk.Label(root, text="اختبار النافذة", font=("Arial", 14))
        label.pack(pady=50)

        # إغلاق النافذة بعد ثانية واحدة
        root.after(1000, root.destroy)
        print("✅ tkinter window test successful")

        return True

    except Exception as e:
        print(f"❌ Basic import error: {e}")
        return False

def test_dependencies():
    """اختبار المكتبات المطلوبة"""
    try:
        print("\n🔍 Testing dependencies...")

        import sqlalchemy
        print("✅ sqlalchemy imported successfully")

        try:
            from PIL import Image
            print("✅ Pillow imported successfully")
        except ImportError:
            print("⚠️ Pillow not found - installing...")
            os.system("pip install Pillow")

        try:
            import openpyxl
            print("✅ openpyxl imported successfully")
        except ImportError:
            print("⚠️ openpyxl not found - installing...")
            os.system("pip install openpyxl")

        return True

    except Exception as e:
        print(f"❌ Dependencies error: {e}")
        return False

def test_local_modules():
    """اختبار الوحدات المحلية"""
    try:
        print("\n🔍 Testing local modules...")

        from utils.config import Config
        print("✅ Config imported successfully")

        from utils.validators import AlgerianValidators
        print("✅ AlgerianValidators imported successfully")

        from database.models import Employee, Wilaya
        print("✅ Database models imported successfully")

        from database.database_manager import DatabaseManager
        print("✅ DatabaseManager imported successfully")

        return True

    except Exception as e:
        print(f"❌ Local modules error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    try:
        print("\n🔍 Testing database...")

        from database.database_manager import DatabaseManager

        db_manager = DatabaseManager()
        print("✅ DatabaseManager created successfully")

        db_manager.create_tables()
        print("✅ Database tables created successfully")

        return True

    except Exception as e:
        print(f"❌ Database error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 Starting comprehensive test...")
    print("=" * 50)

    # اختبار الاستيرادات الأساسية
    if not test_basic_imports():
        print("\n❌ Basic imports failed!")
        return False

    # اختبار المكتبات المطلوبة
    if not test_dependencies():
        print("\n❌ Dependencies test failed!")
        return False

    # اختبار الوحدات المحلية
    if not test_local_modules():
        print("\n❌ Local modules test failed!")
        return False

    # اختبار قاعدة البيانات
    if not test_database():
        print("\n❌ Database test failed!")
        return False

    print("\n" + "=" * 50)
    print("🎉 All tests passed successfully!")
    print("✅ The application is ready to run!")

    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
