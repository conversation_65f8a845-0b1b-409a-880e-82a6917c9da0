@echo off
chcp 65001 > nul
echo ========================================
echo برنامج تسيير مستخدمي الجمارك الجزائرية
echo Algerian Customs Personnel Management System
echo ========================================
echo.

echo التحقق من وجود Python...
python --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✓ تم العثور على Python

echo.
echo اختبار tkinter...
python -c "import tkinter; print('tkinter OK')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ tkinter غير متوفر
    echo يرجى إعادة تثبيت Python مع تحديد "tcl/tk and IDLE"
    pause
    exit /b 1
)

echo ✓ tkinter متوفر

echo.
echo التحقق من المتطلبات الأساسية...
python -c "import sqlalchemy" 2>nul
if %errorlevel% neq 0 (
    echo تثبيت SQLAlchemy...
    pip install sqlalchemy
)

python -c "import PIL" 2>nul
if %errorlevel% neq 0 (
    echo تثبيت Pillow...
    pip install Pillow
)

python -c "import openpyxl" 2>nul
if %errorlevel% neq 0 (
    echo تثبيت openpyxl...
    pip install openpyxl
)

echo ✓ المتطلبات الأساسية متوفرة

echo.
echo تشغيل اختبار بسيط...
python basic_test.py
if %errorlevel% neq 0 (
    echo ❌ فشل الاختبار البسيط
    pause
    exit /b 1
)

echo.
echo تشغيل البرنامج الرئيسي...
python main.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ في تشغيل البرنامج
    echo جرب تشغيل: python run.py
    pause
)

echo.
echo تم إغلاق البرنامج
pause
