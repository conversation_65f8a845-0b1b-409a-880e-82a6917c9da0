# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات
Database Models for Customs Personnel Management System
"""

from sqlalchemy import Column, Integer, String, Date, Boolean, Text, ForeignKey, Float, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

# جداول الإعدادات والمراجع
class Wilaya(Base):
    """جدول الولايات"""
    __tablename__ = 'wilayas'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(2), unique=True, nullable=False)
    name_ar = Column(String(100), nullable=False)
    name_fr = Column(String(100))
    
    # العلاقات
    communes = relationship("Commune", back_populates="wilaya")

class Commune(Base):
    """جدول البلديات"""
    __tablename__ = 'communes'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(6), unique=True, nullable=False)
    name_ar = Column(String(100), nullable=False)
    name_fr = Column(String(100))
    wilaya_id = Column(Integer, ForeignKey('wilayas.id'), nullable=False)
    
    # العلاقات
    wilaya = relationship("Wilaya", back_populates="communes")

class Corps(Base):
    """جدول الأسلاك"""
    __tablename__ = 'corps'
    
    id = Column(Integer, primary_key=True)
    name_ar = Column(String(100), nullable=False)
    name_fr = Column(String(100))
    description = Column(Text)
    
    # العلاقات
    ranks = relationship("Rank", back_populates="corps")

class Rank(Base):
    """جدول الرتب"""
    __tablename__ = 'ranks'
    
    id = Column(Integer, primary_key=True)
    name_ar = Column(String(100), nullable=False)
    name_fr = Column(String(100))
    corps_id = Column(Integer, ForeignKey('corps.id'), nullable=False)
    level = Column(Integer)  # مستوى الرتبة
    
    # العلاقات
    corps = relationship("Corps", back_populates="ranks")

class Directorate(Base):
    """جدول المديريات"""
    __tablename__ = 'directorates'
    
    id = Column(Integer, primary_key=True)
    name_ar = Column(String(200), nullable=False)
    name_fr = Column(String(200))
    code = Column(String(10), unique=True)
    parent_id = Column(Integer, ForeignKey('directorates.id'))
    level = Column(Integer, default=1)  # مستوى التسلسل الهرمي
    
    # العلاقات
    parent = relationship("Directorate", remote_side=[id])
    children = relationship("Directorate")
    services = relationship("Service", back_populates="directorate")

class Service(Base):
    """جدول المصالح"""
    __tablename__ = 'services'
    
    id = Column(Integer, primary_key=True)
    name_ar = Column(String(200), nullable=False)
    name_fr = Column(String(200))
    code = Column(String(10))
    directorate_id = Column(Integer, ForeignKey('directorates.id'), nullable=False)
    
    # العلاقات
    directorate = relationship("Directorate", back_populates="services")
    positions = relationship("Position", back_populates="service")

class Position(Base):
    """جدول الوظائف"""
    __tablename__ = 'positions'
    
    id = Column(Integer, primary_key=True)
    name_ar = Column(String(200), nullable=False)
    name_fr = Column(String(200))
    service_id = Column(Integer, ForeignKey('services.id'), nullable=False)
    description = Column(Text)
    
    # العلاقات
    service = relationship("Service", back_populates="positions")

class Assignment(Base):
    """جدول أماكن التعيين"""
    __tablename__ = 'assignments'
    
    id = Column(Integer, primary_key=True)
    name_ar = Column(String(200), nullable=False)
    name_fr = Column(String(200))
    code = Column(String(10))
    directorate_id = Column(Integer, ForeignKey('directorates.id'))
    service_id = Column(Integer, ForeignKey('services.id'))
    wilaya_id = Column(Integer, ForeignKey('wilayas.id'))
    
    # العلاقات
    directorate = relationship("Directorate")
    service = relationship("Service")
    wilaya = relationship("Wilaya")

# الجدول الرئيسي للموظفين
class Employee(Base):
    """جدول الموظفين الرئيسي"""
    __tablename__ = 'employees'
    
    # البيانات الأساسية
    id = Column(Integer, primary_key=True)
    registration_number = Column(String(6), unique=True, nullable=False)  # رقم التسجيل
    last_name_ar = Column(String(100), nullable=False)  # اللقب
    first_name_ar = Column(String(100), nullable=False)  # الاسم
    last_name_fr = Column(String(100))  # Nom
    first_name_fr = Column(String(100))  # Prenom
    gender = Column(String(10), nullable=False)  # الجنس
    birth_date = Column(Date, nullable=False)  # تاريخ الميلاد
    birth_wilaya_id = Column(Integer, ForeignKey('wilayas.id'))  # ولاية الميلاد
    birth_commune_id = Column(Integer, ForeignKey('communes.id'))  # بلدية الميلاد
    marital_status = Column(String(20), nullable=False)  # الحالة العائلية
    children_count = Column(Integer, default=0)  # عدد الأبناء
    dependents_count = Column(Integer, default=0)  # عدد الأشخاص المتكفل بهم
    blood_type = Column(String(5))  # زمرة الدم
    photo_path = Column(String(500))  # مسار صورة الموظف
    
    # بيانات الاتصال
    phone1 = Column(String(20))  # رقم الهاتف 1
    phone2 = Column(String(20))  # رقم الهاتف 2
    email = Column(String(100))  # البريد الإلكتروني
    main_address = Column(Text)  # العنوان الرئيسي
    secondary_address = Column(Text)  # العنوان الثانوي
    emergency_contact_name = Column(String(200))  # اسم الشخص المتصل به في حالة الضرورة
    emergency_contact_address = Column(Text)  # عنوان الشخص المتصل به
    
    # البيانات المهنية
    employee_status = Column(String(20), default='نشط')  # حالة الموظف
    corps_id = Column(Integer, ForeignKey('corps.id'))  # السلك
    current_rank_id = Column(Integer, ForeignKey('ranks.id'))  # الرتبة الحالية
    rank_promotion_date = Column(Date)  # تاريخ الترقية في الرتبة الحالية
    current_position_id = Column(Integer, ForeignKey('positions.id'))  # الوظيفة الحالية
    position_assignment_date = Column(Date)  # تاريخ التعيين في الوظيفة الحالية
    current_directorate_id = Column(Integer, ForeignKey('directorates.id'))  # المديرية
    current_service_id = Column(Integer, ForeignKey('services.id'))  # المصلحة
    current_assignment_id = Column(Integer, ForeignKey('assignments.id'))  # مكان التعيين
    recruitment_date = Column(Date)  # تاريخ التوظيف
    recruitment_rank_id = Column(Integer, ForeignKey('ranks.id'))  # رتبة التوظيف
    
    # أرقام الوثائق
    social_security_number = Column(String(15))  # رقم الضمان الاجتماعي
    postal_account_number = Column(String(20))  # رقم الحساب الجاري البريدي
    professional_card_number = Column(String(20))  # رقم البطاقة المهنية
    professional_card_issue_date = Column(Date)  # تاريخ صدور البطاقة المهنية
    national_id_number = Column(String(18))  # رقم بطاقة التعريف الوطنية
    national_id_issue_date = Column(Date)  # تاريخ صدور بطاقة التعريف
    national_id_issue_place_id = Column(Integer, ForeignKey('communes.id'))  # مكان صدور بطاقة التعريف
    driving_license_number = Column(String(20))  # رقم رخصة السياقة
    driving_license_category = Column(String(10))  # صنف رخصة السياقة
    driving_license_issue_date = Column(Date)  # تاريخ صدور رخصة السياقة
    driving_license_issue_place_id = Column(Integer, ForeignKey('communes.id'))  # مكان صدور رخصة السياقة
    mutual_card_number = Column(String(20))  # رقم بطاقة التعاضدية
    mutual_card_issue_date = Column(Date)  # تاريخ صدور بطاقة التعاضدية
    practiced_sport = Column(String(100))  # الرياضة الممارسة
    
    # تواريخ النظام
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # العلاقات
    birth_wilaya = relationship("Wilaya", foreign_keys=[birth_wilaya_id])
    birth_commune = relationship("Commune", foreign_keys=[birth_commune_id])
    corps = relationship("Corps")
    current_rank = relationship("Rank", foreign_keys=[current_rank_id])
    recruitment_rank = relationship("Rank", foreign_keys=[recruitment_rank_id])
    current_position = relationship("Position")
    current_directorate = relationship("Directorate")
    current_service = relationship("Service")
    current_assignment = relationship("Assignment")
    national_id_issue_place = relationship("Commune", foreign_keys=[national_id_issue_place_id])
    driving_license_issue_place = relationship("Commune", foreign_keys=[driving_license_issue_place_id])
    
    # الملفات الفرعية
    certificates = relationship("Certificate", back_populates="employee")
    trainings = relationship("Training", back_populates="employee")
    languages = relationship("EmployeeLanguage", back_populates="employee")
    transfers = relationship("Transfer", back_populates="employee")
    annual_leaves = relationship("AnnualLeave", back_populates="employee")
    sick_leaves = relationship("SickLeave", back_populates="employee")
    other_leaves = relationship("OtherLeave", back_populates="employee")
    deposits = relationship("Deposit", back_populates="employee")
    suspensions = relationship("Suspension", back_populates="employee")
    deaths = relationship("Death", back_populates="employee")
    delegations = relationship("Delegation", back_populates="employee")
    resignations = relationship("Resignation", back_populates="employee")
    external_transfers = relationship("ExternalTransfer", back_populates="employee")
    rank_promotions = relationship("RankPromotion", back_populates="employee")
    grade_promotions = relationship("GradePromotion", back_populates="employee")
    spouses = relationship("Spouse", back_populates="employee")
    children = relationship("Child", back_populates="employee")
    dependents = relationship("Dependent", back_populates="employee")
    penalties = relationship("Penalty", back_populates="employee")
    rewards = relationship("Reward", back_populates="employee")

# ملف الشهادات
class Certificate(Base):
    """جدول الشهادات"""
    __tablename__ = 'certificates'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    certificate_type = Column(String(100), nullable=False)  # نوع الشهادة
    specialization = Column(String(200))  # التخصص
    grant_year = Column(Integer)  # سنة المنح
    granting_institution = Column(String(200))  # المؤسسة المانحة

    # العلاقات
    employee = relationship("Employee", back_populates="certificates")

# ملف التكوين
class Training(Base):
    """جدول التكوين"""
    __tablename__ = 'trainings'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    training_subject = Column(String(200), nullable=False)  # موضوع التكوين
    duration = Column(String(50))  # المدة الزمنية
    start_date = Column(Date)  # تاريخ بداية التكوين
    end_date = Column(Date)  # تاريخ نهاية التكوين

    # العلاقات
    employee = relationship("Employee", back_populates="trainings")

# ملف اللغات
class Language(Base):
    """جدول اللغات"""
    __tablename__ = 'languages'

    id = Column(Integer, primary_key=True)
    name_ar = Column(String(50), nullable=False)
    name_fr = Column(String(50))

class EmployeeLanguage(Base):
    """جدول لغات الموظف"""
    __tablename__ = 'employee_languages'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    language_id = Column(Integer, ForeignKey('languages.id'), nullable=False)
    can_write = Column(Boolean, default=False)  # كتابة
    writing_level = Column(String(20))  # مستوى الكتابة
    can_read = Column(Boolean, default=False)  # قراءة
    reading_level = Column(String(20))  # مستوى القراءة

    # العلاقات
    employee = relationship("Employee", back_populates="languages")
    language = relationship("Language")

# ملف التحويلات والتنقلات
class Transfer(Base):
    """جدول التحويلات والتنقلات"""
    __tablename__ = 'transfers'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    directorate_id = Column(Integer, ForeignKey('directorates.id'))  # المديرية
    service_id = Column(Integer, ForeignKey('services.id'))  # المصلحة
    assignment_id = Column(Integer, ForeignKey('assignments.id'))  # مكان التعيين
    installation_date = Column(Date)  # تاريخ التنصيب
    end_date = Column(Date)  # تاريخ انهاء المهام
    duration_days = Column(Integer)  # المدة بالأيام
    position_id = Column(Integer, ForeignKey('positions.id'))  # الوظيفة
    decision_number = Column(String(50))  # رقم المقرر
    decision_date = Column(Date)  # تاريخ المقرر

    # العلاقات
    employee = relationship("Employee", back_populates="transfers")
    directorate = relationship("Directorate")
    service = relationship("Service")
    assignment = relationship("Assignment")
    position = relationship("Position")

# ملف العطل السنوية
class AnnualLeave(Base):
    """جدول العطل السنوية"""
    __tablename__ = 'annual_leaves'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    leave_year = Column(Integer, nullable=False)  # سنة العطلة
    days_count = Column(Integer, nullable=False)  # عدد أيام العطلة
    start_date = Column(Date, nullable=False)  # تاريخ بداية العطلة
    end_date = Column(Date)  # تاريخ نهاية العطلة (محسوب)
    remaining_days = Column(Integer)  # المتبقي
    destination = Column(String(200))  # المكان المقصود
    decision_number = Column(String(50))  # رقم مقرر العطلة
    decision_date = Column(Date)  # تاريخ مقرر العطلة

    # العلاقات
    employee = relationship("Employee", back_populates="annual_leaves")

# ملف العطل المرضية
class SickLeave(Base):
    """جدول العطل المرضية"""
    __tablename__ = 'sick_leaves'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    leave_type = Column(String(100), nullable=False)  # نوع العطلة المرضية
    start_date = Column(Date, nullable=False)  # تاريخ بداية العطلة
    days_count = Column(Integer, nullable=False)  # عدد أيام العطلة
    end_date = Column(Date)  # تاريخ نهاية العطلة (محسوب)
    is_indexed = Column(Boolean, default=False)  # مؤشرة
    internal_medical_control = Column(Boolean, default=False)  # الرقابة الطبية الداخلية
    consultant_doctor_opinion = Column(String(20))  # رأي الطبيب المستشار
    deduction_number = Column(String(50))  # رقم الخصم
    deduction_date = Column(Date)  # تاريخ الخصم

    # العلاقات
    employee = relationship("Employee", back_populates="sick_leaves")

# ملف العطل الأخرى
class OtherLeave(Base):
    """جدول العطل الأخرى"""
    __tablename__ = 'other_leaves'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    leave_type = Column(String(100), nullable=False)  # نوع العطلة
    start_date = Column(Date, nullable=False)  # تاريخ بداية العطلة
    days_count = Column(Integer, nullable=False)  # عدد أيام العطلة
    end_date = Column(Date)  # تاريخ نهاية العطلة (محسوب)
    reason = Column(Text)  # سبب العطلة
    destination = Column(String(200))  # المكان المقصود
    decision_number = Column(String(50))  # رقم مقرر العطلة
    decision_date = Column(Date)  # تاريخ مقرر العطلة
    salary_deduction = Column(Boolean, default=False)  # الخصم من الراتب
    deduction_number = Column(String(50))  # رقم الخصم
    deduction_date = Column(Date)  # تاريخ الخصم

    # العلاقات
    employee = relationship("Employee", back_populates="other_leaves")

# ملف الاستيداع
class Deposit(Base):
    """جدول الاستيداع"""
    __tablename__ = 'deposits'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    period = Column(String(20), nullable=False)  # الفترة
    duration_years = Column(Integer, nullable=False)  # المدة بالسنوات
    reason = Column(String(200))  # سبب الاستيداع
    start_date = Column(Date, nullable=False)  # تاريخ بداية الاستيداع
    end_date = Column(Date)  # تاريخ نهاية الاستيداع (محسوب)
    decision_number = Column(String(50))  # رقم المقرر
    decision_date = Column(Date)  # تاريخ المقرر

    # العلاقات
    employee = relationship("Employee", back_populates="deposits")

# ملف التوقيف
class Suspension(Base):
    """جدول التوقيف"""
    __tablename__ = 'suspensions'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    suspension_date = Column(Date, nullable=False)  # تاريخ التوقيف
    reason = Column(Text)  # سبب التوقيف
    decision_number = Column(String(50))  # رقم مقرر التوقيف
    decision_date = Column(Date)  # تاريخ مقرر التوقيف

    # العلاقات
    employee = relationship("Employee", back_populates="suspensions")

# ملف الوفيات
class Death(Base):
    """جدول الوفيات"""
    __tablename__ = 'deaths'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    death_date = Column(Date, nullable=False)  # تاريخ الوفاة
    cause = Column(String(50))  # السبب (عادية - حادث عمل)
    certificate_number = Column(String(50))  # رقم شهادة الوفاة

    # العلاقات
    employee = relationship("Employee", back_populates="deaths")

# ملف الانتداب
class Delegation(Base):
    """جدول الانتداب"""
    __tablename__ = 'delegations'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    delegation_date = Column(Date, nullable=False)  # تاريخ الانتداب
    duration = Column(String(50))  # مدة الانتداب
    reason = Column(Text)  # سبب الانتداب
    location = Column(String(200))  # مكان الانتداب

    # العلاقات
    employee = relationship("Employee", back_populates="delegations")

# ملف الاستقالة
class Resignation(Base):
    """جدول الاستقالة"""
    __tablename__ = 'resignations'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    request_date = Column(Date, nullable=False)  # تاريخ الطلب
    reason = Column(Text)  # سبب الاستقالة
    is_accepted = Column(Boolean)  # مقبولة
    decision_number = Column(String(50))  # رقم المقرر
    decision_date = Column(Date)  # تاريخ المقرر

    # العلاقات
    employee = relationship("Employee", back_populates="resignations")

# ملف التحويل الخارجي
class ExternalTransfer(Base):
    """جدول التحويل الخارجي"""
    __tablename__ = 'external_transfers'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    end_date = Column(Date, nullable=False)  # تاريخ إنهاء المهام
    transferred_to = Column(String(200))  # المديرية المحول لها
    decision_number = Column(String(50))  # رقم المقرر
    decision_date = Column(Date)  # تاريخ المقرر

    # العلاقات
    employee = relationship("Employee", back_populates="external_transfers")

# ملف الترقية في الرتبة
class RankPromotion(Base):
    """جدول الترقية في الرتبة"""
    __tablename__ = 'rank_promotions'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    old_rank_id = Column(Integer, ForeignKey('ranks.id'))  # الرتبة القديمة
    new_rank_id = Column(Integer, ForeignKey('ranks.id'))  # الرتبة الجديدة
    promotion_type = Column(String(100))  # نوع الترقية
    decision_number = Column(String(50))  # رقم مقرر الترقية
    decision_date = Column(Date)  # تاريخ مقرر الترقية

    # العلاقات
    employee = relationship("Employee", back_populates="rank_promotions")
    old_rank = relationship("Rank", foreign_keys=[old_rank_id])
    new_rank = relationship("Rank", foreign_keys=[new_rank_id])

# ملف الترقية في الدرجة
class GradePromotion(Base):
    """جدول الترقية في الدرجة"""
    __tablename__ = 'grade_promotions'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    grade = Column(Integer, nullable=False)  # درجة الترقية (من 1 إلى 12)
    decision_number = Column(String(50))  # رقم مقرر الترقية
    decision_date = Column(Date)  # تاريخ مقرر الترقية

    # العلاقات
    employee = relationship("Employee", back_populates="grade_promotions")

# ملف الزوجة
class Spouse(Base):
    """جدول الزوجة"""
    __tablename__ = 'spouses'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    first_name = Column(String(100), nullable=False)  # اسم الزوجة
    last_name = Column(String(100), nullable=False)  # لقب الزوجة
    birth_date = Column(Date)  # تاريخ الميلاد
    birth_wilaya_id = Column(Integer, ForeignKey('wilayas.id'))  # ولاية الميلاد
    birth_commune_id = Column(Integer, ForeignKey('communes.id'))  # بلدية الميلاد
    job = Column(String(200))  # وظيفة الزوجة
    workplace = Column(String(200))  # مكان عمل الزوجة

    # العلاقات
    employee = relationship("Employee", back_populates="spouses")
    birth_wilaya = relationship("Wilaya")
    birth_commune = relationship("Commune")

# ملف الأولاد
class Child(Base):
    """جدول الأولاد"""
    __tablename__ = 'children'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    last_name = Column(String(100), nullable=False)  # اللقب
    first_name = Column(String(100), nullable=False)  # الاسم
    gender = Column(String(10), nullable=False)  # الجنس
    is_student = Column(Boolean, default=False)  # متمدرس
    education_level = Column(String(100))  # المستوى الدراسي
    school_institution = Column(String(200))  # مؤسسة التمدرس

    # العلاقات
    employee = relationship("Employee", back_populates="children")

# أشخاص متكفل بهم
class Dependent(Base):
    """جدول الأشخاص المتكفل بهم"""
    __tablename__ = 'dependents'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    last_name = Column(String(100), nullable=False)  # اللقب
    first_name = Column(String(100), nullable=False)  # الاسم
    gender = Column(String(10), nullable=False)  # الجنس
    is_student = Column(Boolean, default=False)  # متمدرس
    education_level = Column(String(100))  # المستوى الدراسي
    school_institution = Column(String(200))  # مؤسسة التمدرس

    # العلاقات
    employee = relationship("Employee", back_populates="dependents")

# جداول العقوبات والمكافآت
class PenaltyType(Base):
    """جدول أنواع العقوبات"""
    __tablename__ = 'penalty_types'

    id = Column(Integer, primary_key=True)
    degree = Column(Integer, nullable=False)  # درجة العقوبة
    name_ar = Column(String(200), nullable=False)  # تحديد العقوبة
    description = Column(Text)

    # العلاقات
    penalties = relationship("Penalty", back_populates="penalty_type")

class Penalty(Base):
    """جدول العقوبات"""
    __tablename__ = 'penalties'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    penalty_type_id = Column(Integer, ForeignKey('penalty_types.id'), nullable=False)
    reason = Column(Text)  # سبب العقوبة
    decision_number = Column(String(50))  # رقم مقرر العقوبة
    decision_date = Column(Date)  # تاريخ مقرر العقوبة

    # العلاقات
    employee = relationship("Employee", back_populates="penalties")
    penalty_type = relationship("PenaltyType", back_populates="penalties")

class Reward(Base):
    """جدول المكافآت"""
    __tablename__ = 'rewards'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    reward_type = Column(String(50), nullable=False)  # نوع المكافأة (مادية - معنوية)
    grant_reason = Column(Text)  # سبب المنح
    granting_authority = Column(String(200))  # الهيئة المانحة
    reward_specification = Column(String(200))  # تحديد المكافأة
    grant_date = Column(Date)  # تاريخ المنح

    # العلاقات
    employee = relationship("Employee", back_populates="rewards")

# ملف بيانات المؤسسة
class Institution(Base):
    """جدول بيانات المؤسسة"""
    __tablename__ = 'institutions'

    id = Column(Integer, primary_key=True)
    name = Column(String(200), nullable=False)  # اسم المؤسسة
    phone = Column(String(20))  # رقم الهاتف
    fax = Column(String(20))  # رقم الفاكس
    email = Column(String(100))  # البريد الإلكتروني
    website = Column(String(200))  # عنوان الموقع
    address = Column(Text)  # عنوان المراسلة
    logo_path = Column(String(500))  # رمز المؤسسة (صورة)
