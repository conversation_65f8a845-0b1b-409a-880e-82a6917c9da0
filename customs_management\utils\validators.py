# -*- coding: utf-8 -*-
"""
مدققات البيانات الجزائرية
Algerian Data Validators
"""

import re
from datetime import datetime

class AlgerianValidators:
    """فئة مدققات البيانات الجزائرية"""
    
    @staticmethod
    def validate_social_security_number(number):
        """
        التحقق من صحة رقم الضمان الاجتماعي الجزائري
        تركيب الرقم: AAMMJJWWWWWKK
        AA: السنة (آخر رقمين من سنة الميلاد)
        MM: الشهر (01-12)
        JJ: اليوم (01-31)
        WWWW: رقم الولاية + رقم تسلسلي
        KK: مفتاح التحقق
        """
        if not number or len(number) != 15:
            return False
        
        if not number.isdigit():
            return False
        
        try:
            year = int(number[0:2])
            month = int(number[2:4])
            day = int(number[4:6])
            wilaya_code = int(number[6:8])
            sequence = int(number[8:13])
            check_key = int(number[13:15])
            
            # التحقق من صحة التاريخ
            if month < 1 or month > 12:
                return False
            
            if day < 1 or day > 31:
                return False
            
            # التحقق من رمز الولاية (01-58)
            if wilaya_code < 1 or wilaya_code > 58:
                return False
            
            # حساب مفتاح التحقق
            base_number = int(number[0:13])
            calculated_key = 97 - (base_number % 97)
            
            return calculated_key == check_key
            
        except ValueError:
            return False
    
    @staticmethod
    def validate_postal_account_number(number):
        """
        التحقق من صحة رقم الحساب الجاري البريدي الجزائري
        تركيب الرقم: NNNNNNNNNNNKK
        NNNNNNNNNNN: 11 رقم للحساب
        KK: مفتاح التحقق
        """
        if not number or len(number) != 13:
            return False
        
        if not number.isdigit():
            return False
        
        try:
            account_number = int(number[0:11])
            check_key = int(number[11:13])
            
            # حساب مفتاح التحقق
            calculated_key = 97 - (account_number % 97)
            
            return calculated_key == check_key
            
        except ValueError:
            return False
    
    @staticmethod
    def validate_national_id_number(number, birth_date=None, gender=None):
        """
        التحقق من صحة رقم بطاقة التعريف الوطنية الجزائرية
        تركيب الرقم: AAMMJJWWWWWWWWWWWK
        AA: السنة (آخر رقمين من سنة الميلاد)
        MM: الشهر (01-12)
        JJ: اليوم (01-31)
        WWWWWWWWWWWW: رقم الولاية + رقم تسلسلي
        K: رقم تحقق
        """
        if not number or len(number) != 18:
            return False
        
        if not number.isdigit():
            return False
        
        try:
            year = int(number[0:2])
            month = int(number[2:4])
            day = int(number[4:6])
            wilaya_code = int(number[6:8])
            
            # التحقق من صحة التاريخ
            if month < 1 or month > 12:
                return False
            
            if day < 1 or day > 31:
                return False
            
            # التحقق من رمز الولاية (01-58)
            if wilaya_code < 1 or wilaya_code > 58:
                return False
            
            # التحقق من تطابق تاريخ الميلاد إذا تم توفيره
            if birth_date:
                try:
                    if isinstance(birth_date, str):
                        birth_date_obj = datetime.strptime(birth_date, "%d/%m/%Y")
                    else:
                        birth_date_obj = birth_date
                    
                    birth_year = birth_date_obj.year % 100
                    birth_month = birth_date_obj.month
                    birth_day = birth_date_obj.day
                    
                    if year != birth_year or month != birth_month or day != birth_day:
                        return False
                        
                except (ValueError, AttributeError):
                    pass
            
            # التحقق من الجنس إذا تم توفيره
            if gender:
                # الرقم قبل الأخير يحدد الجنس (زوجي للإناث، فردي للذكور)
                gender_digit = int(number[16])
                if gender == "ذكر" and gender_digit % 2 == 0:
                    return False
                elif gender == "أنثى" and gender_digit % 2 == 1:
                    return False
            
            return True
            
        except ValueError:
            return False
    
    @staticmethod
    def validate_phone_number(number):
        """
        التحقق من صحة رقم الهاتف الجزائري
        أرقام الهاتف الثابت: 9 أرقام تبدأ بـ 0
        أرقام الهاتف المحمول: 10 أرقام تبدأ بـ 05, 06, 07
        """
        if not number:
            return True  # الرقم اختياري
        
        # إزالة المسافات والرموز
        clean_number = re.sub(r'[\s\-\(\)]', '', number)
        
        # التحقق من أن الرقم يحتوي على أرقام فقط
        if not clean_number.isdigit():
            return False
        
        # هاتف محمول (10 أرقام)
        if len(clean_number) == 10 and clean_number.startswith(('05', '06', '07')):
            return True
        
        # هاتف ثابت (9 أرقام)
        if len(clean_number) == 9 and clean_number.startswith('0'):
            return True
        
        # رقم دولي
        if clean_number.startswith('213'):
            # إزالة رمز الدولة
            local_number = clean_number[3:]
            if len(local_number) == 9 and local_number.startswith(('5', '6', '7')):
                return True
            if len(local_number) == 8:
                return True
        
        return False
    
    @staticmethod
    def validate_email(email):
        """التحقق من صحة البريد الإلكتروني"""
        if not email:
            return True  # البريد الإلكتروني اختياري
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_registration_number(number):
        """
        التحقق من صحة رقم التسجيل
        يجب أن يكون 6 أرقام
        """
        if not number:
            return False
        
        return number.isdigit() and len(number) == 6
    
    @staticmethod
    def validate_date_format(date_string, format_string="%d/%m/%Y"):
        """التحقق من صحة تنسيق التاريخ"""
        if not date_string:
            return False
        
        try:
            datetime.strptime(date_string, format_string)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_age_range(birth_date, min_age=19, max_age=65):
        """التحقق من أن العمر ضمن النطاق المحدد"""
        if not birth_date:
            return False
        
        try:
            if isinstance(birth_date, str):
                birth_date_obj = datetime.strptime(birth_date, "%d/%m/%Y")
            else:
                birth_date_obj = birth_date
            
            today = datetime.now()
            age = (today - birth_date_obj).days // 365
            
            return min_age <= age <= max_age
            
        except (ValueError, AttributeError):
            return False
    
    @staticmethod
    def format_phone_number(number):
        """تنسيق رقم الهاتف"""
        if not number:
            return ""
        
        # إزالة المسافات والرموز
        clean_number = re.sub(r'[\s\-\(\)]', '', number)
        
        if len(clean_number) == 10 and clean_number.startswith(('05', '06', '07')):
            # تنسيق الهاتف المحمول: 05XX XX XX XX
            return f"{clean_number[:4]} {clean_number[4:6]} {clean_number[6:8]} {clean_number[8:]}"
        elif len(clean_number) == 9 and clean_number.startswith('0'):
            # تنسيق الهاتف الثابت: 0XX XX XX XX
            return f"{clean_number[:3]} {clean_number[3:5]} {clean_number[5:7]} {clean_number[7:]}"
        
        return number
    
    @staticmethod
    def format_social_security_number(number):
        """تنسيق رقم الضمان الاجتماعي"""
        if not number or len(number) != 15:
            return number
        
        # تنسيق: AA MM JJ WW WWW KK
        return f"{number[:2]} {number[2:4]} {number[4:6]} {number[6:8]} {number[8:13]} {number[13:]}"
    
    @staticmethod
    def format_postal_account_number(number):
        """تنسيق رقم الحساب الجاري البريدي"""
        if not number or len(number) != 13:
            return number
        
        # تنسيق: NNNNNNNNNNN KK
        return f"{number[:11]} {number[11:]}"
    
    @staticmethod
    def format_national_id_number(number):
        """تنسيق رقم بطاقة التعريف الوطنية"""
        if not number or len(number) != 18:
            return number
        
        # تنسيق: AA MM JJ WW WWWWWWWW K
        return f"{number[:2]} {number[2:4]} {number[4:6]} {number[6:8]} {number[8:16]} {number[16:]}"
