print("Testing Python...")
print("Python is working!")

try:
    import tkinter as tk
    print("tkinter is available")
    
    root = tk.Tk()
    root.title("Test")
    root.geometry("300x200")
    
    label = tk.Label(root, text="البرنامج يعمل!")
    label.pack(pady=50)
    
    root.after(2000, root.destroy)  # إغلاق بعد ثانيتين
    root.mainloop()
    
    print("tkinter test completed successfully")
    
except Exception as e:
    print(f"Error: {e}")

print("Test finished")
