# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للبرنامج
Main Window for Customs Personnel Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox, Menu
from ttkthemes import ThemedTk, ThemedStyle
import os
from PIL import Image, ImageTk

from utils.config import Config
from .employee_management import EmployeeManagementFrame
from .settings_management import SettingsManagementFrame
from .reports_management import ReportsManagementFrame

class MainWindow:
    """النافذة الرئيسية للبرنامج"""
    
    def __init__(self, root, db_manager):
        self.root = root
        self.db_manager = db_manager
        
        # إعداد النافذة الرئيسية
        self.setup_main_window()
        
        # إنشاء القائمة الرئيسية
        self.create_menu()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء المحتوى الرئيسي
        self.create_main_content()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
    
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        # تعيين عنوان النافذة
        self.root.title(Config.WINDOW_TITLE)
        
        # تعيين حجم النافذة
        self.root.geometry(Config.WINDOW_SIZE)
        
        # تعيين الحد الأدنى لحجم النافذة
        self.root.minsize(1000, 600)
        
        # توسيط النافذة على الشاشة
        self.center_window()
        
        # تطبيق الثيم
        self.apply_theme()
        
        # تعيين أيقونة النافذة (إذا كانت متوفرة)
        self.set_window_icon()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def apply_theme(self):
        """تطبيق الثيم على الواجهة"""
        try:
            style = ThemedStyle(self.root)
            style.set_theme(Config.THEME)
            
            # تخصيص الألوان
            style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
            style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
            style.configure('Custom.TButton', padding=(10, 5))
            
        except Exception as e:
            print(f"خطأ في تطبيق الثيم: {e}")
    
    def set_window_icon(self):
        """تعيين أيقونة النافذة"""
        try:
            icon_path = os.path.join(Config.BASE_DIR, 'assets', 'icon.ico')
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception as e:
            print(f"خطأ في تحميل الأيقونة: {e}")
    
    def create_menu(self):
        """إنشاء القائمة الرئيسية"""
        menubar = Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="موظف جديد", command=self.new_employee)
        file_menu.add_separator()
        file_menu.add_command(label="نسخ احتياطي", command=self.backup_database)
        file_menu.add_command(label="استعادة", command=self.restore_database)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.exit_application)
        
        # قائمة الموظفين
        employee_menu = Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الموظفين", menu=employee_menu)
        employee_menu.add_command(label="إدارة الموظفين", command=self.show_employee_management)
        employee_menu.add_command(label="بحث عن موظف", command=self.search_employee)
        employee_menu.add_separator()
        employee_menu.add_command(label="العطل السنوية", command=self.manage_annual_leaves)
        employee_menu.add_command(label="العطل المرضية", command=self.manage_sick_leaves)
        employee_menu.add_command(label="التحويلات", command=self.manage_transfers)
        
        # قائمة التقارير
        reports_menu = Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="تقرير الموظفين", command=self.show_employee_report)
        reports_menu.add_command(label="تقرير العطل", command=self.show_leave_report)
        reports_menu.add_command(label="إحصائيات", command=self.show_statistics)
        
        # قائمة الإعدادات
        settings_menu = Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الإعدادات", menu=settings_menu)
        settings_menu.add_command(label="التقسيم الإداري", command=self.manage_administrative_division)
        settings_menu.add_command(label="الرتب والأسلاك", command=self.manage_ranks_corps)
        settings_menu.add_command(label="المديريات والمصالح", command=self.manage_directorates_services)
        settings_menu.add_command(label="الوظائف", command=self.manage_positions)
        
        # قائمة المساعدة
        help_menu = Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.show_user_guide)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar = ttk.Frame(self.root)
        self.toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
        
        # أزرار شريط الأدوات
        ttk.Button(
            self.toolbar,
            text="موظف جديد",
            style='Custom.TButton',
            command=self.new_employee
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        ttk.Button(
            self.toolbar,
            text="بحث",
            style='Custom.TButton',
            command=self.search_employee
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            self.toolbar,
            text="تقارير",
            style='Custom.TButton',
            command=self.show_reports
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        ttk.Button(
            self.toolbar,
            text="إعدادات",
            style='Custom.TButton',
            command=self.show_settings
        ).pack(side=tk.LEFT, padx=2)
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # إطار رئيسي
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # إنشاء Notebook للتبويبات
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب الصفحة الرئيسية
        self.create_home_tab()
        
        # تبويب إدارة الموظفين
        self.employee_frame = EmployeeManagementFrame(self.notebook, self.db_manager)
        self.notebook.add(self.employee_frame, text="إدارة الموظفين")
        
        # تبويب التقارير
        self.reports_frame = ReportsManagementFrame(self.notebook, self.db_manager)
        self.notebook.add(self.reports_frame, text="التقارير")
        
        # تبويب الإعدادات
        self.settings_frame = SettingsManagementFrame(self.notebook, self.db_manager)
        self.notebook.add(self.settings_frame, text="الإعدادات")
    
    def create_home_tab(self):
        """إنشاء تبويب الصفحة الرئيسية"""
        self.home_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.home_frame, text="الصفحة الرئيسية")
        
        # عنوان البرنامج
        title_label = ttk.Label(
            self.home_frame,
            text="برنامج تسيير مستخدمي الجمارك الجزائرية",
            style='Title.TLabel'
        )
        title_label.pack(pady=20)
        
        # إطار الإحصائيات السريعة
        stats_frame = ttk.LabelFrame(self.home_frame, text="إحصائيات سريعة", padding=10)
        stats_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # عرض الإحصائيات
        self.update_statistics()
        
        # إطار الروابط السريعة
        quick_links_frame = ttk.LabelFrame(self.home_frame, text="روابط سريعة", padding=10)
        quick_links_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # أزرار الروابط السريعة
        buttons_frame = ttk.Frame(quick_links_frame)
        buttons_frame.pack()
        
        ttk.Button(
            buttons_frame,
            text="إضافة موظف جديد",
            style='Custom.TButton',
            command=self.new_employee
        ).grid(row=0, column=0, padx=10, pady=5)
        
        ttk.Button(
            buttons_frame,
            text="بحث عن موظف",
            style='Custom.TButton',
            command=self.search_employee
        ).grid(row=0, column=1, padx=10, pady=5)
        
        ttk.Button(
            buttons_frame,
            text="تقرير شامل",
            style='Custom.TButton',
            command=self.show_comprehensive_report
        ).grid(row=1, column=0, padx=10, pady=5)
        
        ttk.Button(
            buttons_frame,
            text="نسخ احتياطي",
            style='Custom.TButton',
            command=self.backup_database
        ).grid(row=1, column=1, padx=10, pady=5)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_label = ttk.Label(self.status_bar, text="جاهز")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # معلومات قاعدة البيانات
        self.db_status_label = ttk.Label(self.status_bar, text="")
        self.db_status_label.pack(side=tk.RIGHT, padx=5)
        
        self.update_status("جاهز")
    
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_label.config(text=message)
        self.root.update_idletasks()
    
    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            session = self.db_manager.get_session()
            from database.models import Employee
            
            total_employees = session.query(Employee).count()
            active_employees = session.query(Employee).filter_by(employee_status='نشط').count()
            
            stats_text = f"إجمالي الموظفين: {total_employees} | الموظفين النشطين: {active_employees}"
            
            if hasattr(self, 'stats_label'):
                self.stats_label.config(text=stats_text)
            else:
                self.stats_label = ttk.Label(self.home_frame, text=stats_text)
                self.stats_label.pack(pady=10)
            
            session.close()
            
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")
    
    # دوال الأحداث
    def new_employee(self):
        """إضافة موظف جديد"""
        self.notebook.select(1)  # الانتقال إلى تبويب إدارة الموظفين
        self.employee_frame.new_employee()
    
    def search_employee(self):
        """البحث عن موظف"""
        self.notebook.select(1)
        self.employee_frame.search_employee()
    
    def show_employee_management(self):
        """عرض إدارة الموظفين"""
        self.notebook.select(1)
    
    def show_reports(self):
        """عرض التقارير"""
        self.notebook.select(2)
    
    def show_settings(self):
        """عرض الإعدادات"""
        self.notebook.select(3)
    
    def manage_annual_leaves(self):
        """إدارة العطل السنوية"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def manage_sick_leaves(self):
        """إدارة العطل المرضية"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def manage_transfers(self):
        """إدارة التحويلات"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def show_employee_report(self):
        """عرض تقرير الموظفين"""
        self.show_reports()
    
    def show_leave_report(self):
        """عرض تقرير العطل"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def show_statistics(self):
        """عرض الإحصائيات"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def show_comprehensive_report(self):
        """عرض تقرير شامل"""
        self.show_reports()
    
    def manage_administrative_division(self):
        """إدارة التقسيم الإداري"""
        self.show_settings()
    
    def manage_ranks_corps(self):
        """إدارة الرتب والأسلاك"""
        self.show_settings()
    
    def manage_directorates_services(self):
        """إدارة المديريات والمصالح"""
        self.show_settings()
    
    def manage_positions(self):
        """إدارة الوظائف"""
        self.show_settings()
    
    def backup_database(self):
        """نسخ احتياطي لقاعدة البيانات"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def restore_database(self):
        """استعادة قاعدة البيانات"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def show_user_guide(self):
        """عرض دليل المستخدم"""
        messagebox.showinfo("دليل المستخدم", "دليل المستخدم سيكون متاحاً قريباً")
    
    def show_about(self):
        """عرض معلومات حول البرنامج"""
        about_text = """
برنامج تسيير مستخدمي الجمارك الجزائرية
الإصدار 1.0

تم تطويره باستخدام:
- Python 3.x
- Tkinter للواجهة
- SQLAlchemy لقاعدة البيانات
- SQLite لتخزين البيانات

© 2025 - جميع الحقوق محفوظة
        """
        messagebox.showinfo("حول البرنامج", about_text)
    
    def exit_application(self):
        """الخروج من البرنامج"""
        if messagebox.askokcancel("خروج", "هل تريد الخروج من البرنامج؟"):
            self.db_manager.close()
            self.root.quit()
