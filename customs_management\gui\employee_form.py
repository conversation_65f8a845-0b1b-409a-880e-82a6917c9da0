# -*- coding: utf-8 -*-
"""
نموذج إدخال وتعديل بيانات الموظف
Employee Form for Data Entry and Editing
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, date
from PIL import Image, ImageTk
import os
import re

from database.models import Employee, Wilaya, Commune, Corps, Rank, Directorate, Service, Position
from utils.config import Config
from utils.validators import AlgerianValidators

class EmployeeFormWindow:
    """نافذة نموذج الموظف"""
    
    def __init__(self, parent, db_manager, employee_id=None):
        self.parent = parent
        self.db_manager = db_manager
        self.employee_id = employee_id
        self.is_edit_mode = employee_id is not None
        
        # إنشاء النافذة
        self.create_window()
        
        # تحميل البيانات المرجعية
        self.load_reference_data()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تحميل بيانات الموظف للتعديل
        if self.is_edit_mode:
            self.load_employee_data()
    
    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Toplevel(self.parent)
        title = "تعديل موظف" if self.is_edit_mode else "موظف جديد"
        self.window.title(title)
        self.window.geometry("900x700")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة modal
        self.window.transient(self.parent)
        self.window.grab_set()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def load_reference_data(self):
        """تحميل البيانات المرجعية"""
        session = self.db_manager.get_session()
        
        try:
            # تحميل الولايات
            self.wilayas = session.query(Wilaya).order_by(Wilaya.name_ar).all()
            
            # تحميل الأسلاك
            self.corps = session.query(Corps).order_by(Corps.name_ar).all()
            
            # تحميل المديريات
            self.directorates = session.query(Directorate).order_by(Directorate.name_ar).all()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات المرجعية:\n{str(e)}")
        finally:
            session.close()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إنشاء Notebook للتبويبات
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # تبويب البيانات الأساسية
        self.create_basic_info_tab()
        
        # تبويب بيانات الاتصال
        self.create_contact_info_tab()
        
        # تبويب البيانات المهنية
        self.create_professional_info_tab()
        
        # إطار الأزرار
        self.create_buttons_frame()
    
    def create_basic_info_tab(self):
        """إنشاء تبويب البيانات الأساسية"""
        self.basic_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.basic_frame, text="البيانات الأساسية")
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(self.basic_frame)
        scrollbar = ttk.Scrollbar(self.basic_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط التمرير
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # المتغيرات
        self.registration_number_var = tk.StringVar()
        self.last_name_ar_var = tk.StringVar()
        self.first_name_ar_var = tk.StringVar()
        self.last_name_fr_var = tk.StringVar()
        self.first_name_fr_var = tk.StringVar()
        self.gender_var = tk.StringVar()
        self.birth_date_var = tk.StringVar()
        self.birth_wilaya_var = tk.StringVar()
        self.birth_commune_var = tk.StringVar()
        self.marital_status_var = tk.StringVar()
        self.children_count_var = tk.IntVar()
        self.dependents_count_var = tk.IntVar()
        self.blood_type_var = tk.StringVar()
        
        # الحقول
        row = 0
        
        # رقم التسجيل
        ttk.Label(scrollable_frame, text="رقم التسجيل *:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        self.registration_entry = ttk.Entry(scrollable_frame, textvariable=self.registration_number_var, width=20)
        self.registration_entry.grid(row=row, column=1, padx=5, pady=2)
        row += 1
        
        # اللقب بالعربية
        ttk.Label(scrollable_frame, text="اللقب *:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(scrollable_frame, textvariable=self.last_name_ar_var, width=30).grid(row=row, column=1, padx=5, pady=2)
        row += 1
        
        # الاسم بالعربية
        ttk.Label(scrollable_frame, text="الاسم *:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(scrollable_frame, textvariable=self.first_name_ar_var, width=30).grid(row=row, column=1, padx=5, pady=2)
        row += 1
        
        # اللقب بالفرنسية
        ttk.Label(scrollable_frame, text="Nom:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(scrollable_frame, textvariable=self.last_name_fr_var, width=30).grid(row=row, column=1, padx=5, pady=2)
        row += 1
        
        # الاسم بالفرنسية
        ttk.Label(scrollable_frame, text="Prénom:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(scrollable_frame, textvariable=self.first_name_fr_var, width=30).grid(row=row, column=1, padx=5, pady=2)
        row += 1
        
        # الجنس
        ttk.Label(scrollable_frame, text="الجنس *:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        gender_combo = ttk.Combobox(
            scrollable_frame,
            textvariable=self.gender_var,
            values=["ذكر", "أنثى"],
            state="readonly",
            width=15
        )
        gender_combo.grid(row=row, column=1, padx=5, pady=2, sticky=tk.W)
        row += 1
        
        # تاريخ الميلاد
        ttk.Label(scrollable_frame, text="تاريخ الميلاد *:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        birth_date_frame = ttk.Frame(scrollable_frame)
        birth_date_frame.grid(row=row, column=1, padx=5, pady=2, sticky=tk.W)
        
        ttk.Entry(birth_date_frame, textvariable=self.birth_date_var, width=15).pack(side=tk.LEFT)
        ttk.Label(birth_date_frame, text="(يوم/شهر/سنة)").pack(side=tk.LEFT, padx=5)
        row += 1
        
        # ولاية الميلاد
        ttk.Label(scrollable_frame, text="ولاية الميلاد *:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        self.birth_wilaya_combo = ttk.Combobox(
            scrollable_frame,
            textvariable=self.birth_wilaya_var,
            state="readonly",
            width=25
        )
        self.birth_wilaya_combo.grid(row=row, column=1, padx=5, pady=2, sticky=tk.W)
        self.birth_wilaya_combo.bind('<<ComboboxSelected>>', self.on_birth_wilaya_change)
        row += 1
        
        # بلدية الميلاد
        ttk.Label(scrollable_frame, text="بلدية الميلاد *:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        self.birth_commune_combo = ttk.Combobox(
            scrollable_frame,
            textvariable=self.birth_commune_var,
            state="readonly",
            width=25
        )
        self.birth_commune_combo.grid(row=row, column=1, padx=5, pady=2, sticky=tk.W)
        row += 1
        
        # الحالة العائلية
        ttk.Label(scrollable_frame, text="الحالة العائلية *:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        marital_combo = ttk.Combobox(
            scrollable_frame,
            textvariable=self.marital_status_var,
            values=["أعزب", "متزوج", "مطلق", "أرمل"],
            state="readonly",
            width=15
        )
        marital_combo.grid(row=row, column=1, padx=5, pady=2, sticky=tk.W)
        marital_combo.bind('<<ComboboxSelected>>', self.on_marital_status_change)
        row += 1
        
        # عدد الأبناء
        ttk.Label(scrollable_frame, text="عدد الأبناء:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        self.children_spinbox = ttk.Spinbox(
            scrollable_frame,
            from_=0,
            to=20,
            textvariable=self.children_count_var,
            width=10,
            state="disabled"
        )
        self.children_spinbox.grid(row=row, column=1, padx=5, pady=2, sticky=tk.W)
        row += 1
        
        # عدد الأشخاص المتكفل بهم
        ttk.Label(scrollable_frame, text="عدد الأشخاص المتكفل بهم:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(
            scrollable_frame,
            from_=0,
            to=20,
            textvariable=self.dependents_count_var,
            width=10
        ).grid(row=row, column=1, padx=5, pady=2, sticky=tk.W)
        row += 1
        
        # زمرة الدم
        ttk.Label(scrollable_frame, text="زمرة الدم:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        blood_combo = ttk.Combobox(
            scrollable_frame,
            textvariable=self.blood_type_var,
            values=["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-"],
            state="readonly",
            width=10
        )
        blood_combo.grid(row=row, column=1, padx=5, pady=2, sticky=tk.W)
        row += 1
        
        # صورة الموظف
        ttk.Label(scrollable_frame, text="صورة الموظف:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        photo_frame = ttk.Frame(scrollable_frame)
        photo_frame.grid(row=row, column=1, padx=5, pady=2, sticky=tk.W)
        
        self.photo_label = ttk.Label(photo_frame, text="لا توجد صورة", relief=tk.SUNKEN, width=20)
        self.photo_label.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(photo_frame, text="اختيار صورة", command=self.select_photo).pack(side=tk.LEFT, padx=5)
        ttk.Button(photo_frame, text="حذف الصورة", command=self.remove_photo).pack(side=tk.LEFT, padx=5)
        
        # تحديث قائمة الولايات
        self.update_wilayas_combo()
    
    def create_contact_info_tab(self):
        """إنشاء تبويب بيانات الاتصال"""
        self.contact_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.contact_frame, text="بيانات الاتصال")
        
        # المتغيرات
        self.phone1_var = tk.StringVar()
        self.phone2_var = tk.StringVar()
        self.email_var = tk.StringVar()
        self.main_address_var = tk.StringVar()
        self.secondary_address_var = tk.StringVar()
        self.emergency_contact_name_var = tk.StringVar()
        self.emergency_contact_address_var = tk.StringVar()
        
        # الحقول
        row = 0
        
        # رقم الهاتف 1
        ttk.Label(self.contact_frame, text="رقم الهاتف 1:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(self.contact_frame, textvariable=self.phone1_var, width=30).grid(row=row, column=1, padx=5, pady=5)
        row += 1
        
        # رقم الهاتف 2
        ttk.Label(self.contact_frame, text="رقم الهاتف 2:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(self.contact_frame, textvariable=self.phone2_var, width=30).grid(row=row, column=1, padx=5, pady=5)
        row += 1
        
        # البريد الإلكتروني
        ttk.Label(self.contact_frame, text="البريد الإلكتروني:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(self.contact_frame, textvariable=self.email_var, width=40).grid(row=row, column=1, padx=5, pady=5)
        row += 1
        
        # العنوان الرئيسي
        ttk.Label(self.contact_frame, text="العنوان الرئيسي:").grid(row=row, column=0, sticky=tk.NW, padx=5, pady=5)
        main_address_text = tk.Text(self.contact_frame, width=50, height=3)
        main_address_text.grid(row=row, column=1, padx=5, pady=5)
        self.main_address_text = main_address_text
        row += 1
        
        # العنوان الثانوي
        ttk.Label(self.contact_frame, text="العنوان الثانوي:").grid(row=row, column=0, sticky=tk.NW, padx=5, pady=5)
        secondary_address_text = tk.Text(self.contact_frame, width=50, height=3)
        secondary_address_text.grid(row=row, column=1, padx=5, pady=5)
        self.secondary_address_text = secondary_address_text
        row += 1
        
        # اسم الشخص المتصل به في حالة الضرورة
        ttk.Label(self.contact_frame, text="الشخص المتصل به في حالة الضرورة:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(self.contact_frame, textvariable=self.emergency_contact_name_var, width=40).grid(row=row, column=1, padx=5, pady=5)
        row += 1
        
        # عنوان الشخص المتصل به
        ttk.Label(self.contact_frame, text="عنوان الشخص المتصل به:").grid(row=row, column=0, sticky=tk.NW, padx=5, pady=5)
        emergency_address_text = tk.Text(self.contact_frame, width=50, height=3)
        emergency_address_text.grid(row=row, column=1, padx=5, pady=5)
        self.emergency_address_text = emergency_address_text
    
    def create_professional_info_tab(self):
        """إنشاء تبويب البيانات المهنية"""
        self.professional_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.professional_frame, text="البيانات المهنية")
        
        # المتغيرات
        self.employee_status_var = tk.StringVar()
        self.corps_var = tk.StringVar()
        self.current_rank_var = tk.StringVar()
        self.rank_promotion_date_var = tk.StringVar()
        self.current_position_var = tk.StringVar()
        self.position_assignment_date_var = tk.StringVar()
        self.current_directorate_var = tk.StringVar()
        self.current_service_var = tk.StringVar()
        self.recruitment_date_var = tk.StringVar()
        self.recruitment_rank_var = tk.StringVar()
        self.social_security_number_var = tk.StringVar()
        self.postal_account_number_var = tk.StringVar()
        self.professional_card_number_var = tk.StringVar()
        self.professional_card_issue_date_var = tk.StringVar()
        self.national_id_number_var = tk.StringVar()
        self.national_id_issue_date_var = tk.StringVar()
        
        # الحقول
        row = 0
        
        # حالة الموظف
        ttk.Label(self.professional_frame, text="حالة الموظف *:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        status_combo = ttk.Combobox(
            self.professional_frame,
            textvariable=self.employee_status_var,
            values=["نشط", "تحويل", "موقف", "استيداع", "منتدب", "متوفي", "مفصول", "مستقيل"],
            state="readonly",
            width=20
        )
        status_combo.grid(row=row, column=1, padx=5, pady=5, sticky=tk.W)
        row += 1
        
        # السلك
        ttk.Label(self.professional_frame, text="السلك *:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        self.corps_combo = ttk.Combobox(
            self.professional_frame,
            textvariable=self.corps_var,
            state="readonly",
            width=30
        )
        self.corps_combo.grid(row=row, column=1, padx=5, pady=5, sticky=tk.W)
        self.corps_combo.bind('<<ComboboxSelected>>', self.on_corps_change)
        row += 1
        
        # الرتبة الحالية
        ttk.Label(self.professional_frame, text="الرتبة الحالية *:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        self.current_rank_combo = ttk.Combobox(
            self.professional_frame,
            textvariable=self.current_rank_var,
            state="readonly",
            width=30
        )
        self.current_rank_combo.grid(row=row, column=1, padx=5, pady=5, sticky=tk.W)
        row += 1
        
        # تاريخ الترقية في الرتبة الحالية
        ttk.Label(self.professional_frame, text="تاريخ الترقية في الرتبة الحالية:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(self.professional_frame, textvariable=self.rank_promotion_date_var, width=20).grid(row=row, column=1, padx=5, pady=5, sticky=tk.W)
        row += 1
        
        # المديرية
        ttk.Label(self.professional_frame, text="المديرية *:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        self.directorate_combo = ttk.Combobox(
            self.professional_frame,
            textvariable=self.current_directorate_var,
            state="readonly",
            width=40
        )
        self.directorate_combo.grid(row=row, column=1, padx=5, pady=5, sticky=tk.W)
        self.directorate_combo.bind('<<ComboboxSelected>>', self.on_directorate_change)
        row += 1
        
        # تاريخ التوظيف
        ttk.Label(self.professional_frame, text="تاريخ التوظيف *:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(self.professional_frame, textvariable=self.recruitment_date_var, width=20).grid(row=row, column=1, padx=5, pady=5, sticky=tk.W)
        row += 1
        
        # رقم الضمان الاجتماعي
        ttk.Label(self.professional_frame, text="رقم الضمان الاجتماعي:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        social_security_frame = ttk.Frame(self.professional_frame)
        social_security_frame.grid(row=row, column=1, padx=5, pady=5, sticky=tk.W)
        
        self.social_security_entry = ttk.Entry(social_security_frame, textvariable=self.social_security_number_var, width=20)
        self.social_security_entry.pack(side=tk.LEFT)
        self.social_security_entry.bind('<KeyRelease>', self.validate_social_security)
        
        self.social_security_status = ttk.Label(social_security_frame, text="", foreground="red")
        self.social_security_status.pack(side=tk.LEFT, padx=5)
        row += 1
        
        # رقم الحساب الجاري البريدي
        ttk.Label(self.professional_frame, text="رقم الحساب الجاري البريدي:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        postal_account_frame = ttk.Frame(self.professional_frame)
        postal_account_frame.grid(row=row, column=1, padx=5, pady=5, sticky=tk.W)
        
        self.postal_account_entry = ttk.Entry(postal_account_frame, textvariable=self.postal_account_number_var, width=20)
        self.postal_account_entry.pack(side=tk.LEFT)
        self.postal_account_entry.bind('<KeyRelease>', self.validate_postal_account)
        
        self.postal_account_status = ttk.Label(postal_account_frame, text="", foreground="red")
        self.postal_account_status.pack(side=tk.LEFT, padx=5)
        row += 1
        
        # رقم بطاقة التعريف الوطنية
        ttk.Label(self.professional_frame, text="رقم بطاقة التعريف الوطنية:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        national_id_frame = ttk.Frame(self.professional_frame)
        national_id_frame.grid(row=row, column=1, padx=5, pady=5, sticky=tk.W)
        
        self.national_id_entry = ttk.Entry(national_id_frame, textvariable=self.national_id_number_var, width=20)
        self.national_id_entry.pack(side=tk.LEFT)
        self.national_id_entry.bind('<KeyRelease>', self.validate_national_id)
        
        self.national_id_status = ttk.Label(national_id_frame, text="", foreground="red")
        self.national_id_status.pack(side=tk.LEFT, padx=5)
        
        # تحديث القوائم
        self.update_corps_combo()
        self.update_directorates_combo()
    
    def create_buttons_frame(self):
        """إنشاء إطار الأزرار"""
        buttons_frame = ttk.Frame(self.window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # أزرار العمليات
        ttk.Button(
            buttons_frame,
            text="حفظ",
            command=self.save_employee
        ).pack(side=tk.RIGHT, padx=5)
        
        ttk.Button(
            buttons_frame,
            text="إلغاء",
            command=self.cancel
        ).pack(side=tk.RIGHT, padx=5)
        
        if self.is_edit_mode:
            ttk.Button(
                buttons_frame,
                text="حذف",
                command=self.delete_employee
            ).pack(side=tk.RIGHT, padx=5)
    
    def update_wilayas_combo(self):
        """تحديث قائمة الولايات"""
        wilaya_names = [wilaya.name_ar for wilaya in self.wilayas]
        self.birth_wilaya_combo['values'] = wilaya_names
    
    def update_corps_combo(self):
        """تحديث قائمة الأسلاك"""
        corps_names = [corps.name_ar for corps in self.corps]
        self.corps_combo['values'] = corps_names
    
    def update_directorates_combo(self):
        """تحديث قائمة المديريات"""
        directorate_names = [directorate.name_ar for directorate in self.directorates]
        self.directorate_combo['values'] = directorate_names
    
    def on_birth_wilaya_change(self, event=None):
        """عند تغيير ولاية الميلاد"""
        selected_wilaya_name = self.birth_wilaya_var.get()
        selected_wilaya = next((w for w in self.wilayas if w.name_ar == selected_wilaya_name), None)
        
        if selected_wilaya:
            session = self.db_manager.get_session()
            communes = session.query(Commune).filter_by(wilaya_id=selected_wilaya.id).order_by(Commune.name_ar).all()
            commune_names = [commune.name_ar for commune in communes]
            self.birth_commune_combo['values'] = commune_names
            session.close()
        else:
            self.birth_commune_combo['values'] = []
        
        self.birth_commune_var.set("")
    
    def on_marital_status_change(self, event=None):
        """عند تغيير الحالة العائلية"""
        marital_status = self.marital_status_var.get()
        if marital_status == "أعزب":
            self.children_spinbox.config(state="disabled")
            self.children_count_var.set(0)
        else:
            self.children_spinbox.config(state="normal")
    
    def on_corps_change(self, event=None):
        """عند تغيير السلك"""
        selected_corps_name = self.corps_var.get()
        selected_corps = next((c for c in self.corps if c.name_ar == selected_corps_name), None)
        
        if selected_corps:
            session = self.db_manager.get_session()
            ranks = session.query(Rank).filter_by(corps_id=selected_corps.id).order_by(Rank.level).all()
            rank_names = [rank.name_ar for rank in ranks]
            self.current_rank_combo['values'] = rank_names
            session.close()
        else:
            self.current_rank_combo['values'] = []
        
        self.current_rank_var.set("")
    
    def on_directorate_change(self, event=None):
        """عند تغيير المديرية"""
        # يمكن إضافة تحديث المصالح هنا لاحقاً
        pass
    
    def validate_social_security(self, event=None):
        """التحقق من صحة رقم الضمان الاجتماعي"""
        number = self.social_security_number_var.get()
        if number:
            if AlgerianValidators.validate_social_security_number(number):
                self.social_security_status.config(text="✓", foreground="green")
            else:
                self.social_security_status.config(text="✗", foreground="red")
        else:
            self.social_security_status.config(text="")
    
    def validate_postal_account(self, event=None):
        """التحقق من صحة رقم الحساب الجاري البريدي"""
        number = self.postal_account_number_var.get()
        if number:
            if AlgerianValidators.validate_postal_account_number(number):
                self.postal_account_status.config(text="✓", foreground="green")
            else:
                self.postal_account_status.config(text="✗", foreground="red")
        else:
            self.postal_account_status.config(text="")
    
    def validate_national_id(self, event=None):
        """التحقق من صحة رقم بطاقة التعريف الوطنية"""
        number = self.national_id_number_var.get()
        if number:
            birth_date = self.birth_date_var.get()
            gender = self.gender_var.get()
            
            if AlgerianValidators.validate_national_id_number(number, birth_date, gender):
                self.national_id_status.config(text="✓", foreground="green")
            else:
                self.national_id_status.config(text="✗", foreground="red")
        else:
            self.national_id_status.config(text="")
    
    def select_photo(self):
        """اختيار صورة الموظف"""
        file_path = filedialog.askopenfilename(
            title="اختيار صورة الموظف",
            filetypes=[
                ("ملفات الصور", "*.jpg *.jpeg *.png *.bmp"),
                ("جميع الملفات", "*.*")
            ]
        )
        
        if file_path:
            try:
                # تحميل وتغيير حجم الصورة
                image = Image.open(file_path)
                image = image.resize(Config.MAX_IMAGE_SIZE, Image.Resampling.LANCZOS)
                
                # عرض الصورة
                photo = ImageTk.PhotoImage(image)
                self.photo_label.config(image=photo, text="")
                self.photo_label.image = photo  # الاحتفاظ بمرجع
                
                # حفظ مسار الصورة
                self.selected_photo_path = file_path
                
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في تحميل الصورة:\n{str(e)}")
    
    def remove_photo(self):
        """حذف صورة الموظف"""
        self.photo_label.config(image="", text="لا توجد صورة")
        self.photo_label.image = None
        self.selected_photo_path = None
    
    def load_employee_data(self):
        """تحميل بيانات الموظف للتعديل"""
        if not self.employee_id:
            return
        
        try:
            session = self.db_manager.get_session()
            employee = session.query(Employee).get(self.employee_id)
            
            if employee:
                # البيانات الأساسية
                self.registration_number_var.set(employee.registration_number or "")
                self.last_name_ar_var.set(employee.last_name_ar or "")
                self.first_name_ar_var.set(employee.first_name_ar or "")
                self.last_name_fr_var.set(employee.last_name_fr or "")
                self.first_name_fr_var.set(employee.first_name_fr or "")
                self.gender_var.set(employee.gender or "")
                
                if employee.birth_date:
                    self.birth_date_var.set(employee.birth_date.strftime("%d/%m/%Y"))
                
                if employee.birth_wilaya:
                    self.birth_wilaya_var.set(employee.birth_wilaya.name_ar)
                    self.on_birth_wilaya_change()
                
                if employee.birth_commune:
                    self.birth_commune_var.set(employee.birth_commune.name_ar)
                
                self.marital_status_var.set(employee.marital_status or "")
                self.children_count_var.set(employee.children_count or 0)
                self.dependents_count_var.set(employee.dependents_count or 0)
                self.blood_type_var.set(employee.blood_type or "")
                
                # بيانات الاتصال
                self.phone1_var.set(employee.phone1 or "")
                self.phone2_var.set(employee.phone2 or "")
                self.email_var.set(employee.email or "")
                
                if employee.main_address:
                    self.main_address_text.insert('1.0', employee.main_address)
                
                if employee.secondary_address:
                    self.secondary_address_text.insert('1.0', employee.secondary_address)
                
                self.emergency_contact_name_var.set(employee.emergency_contact_name or "")
                
                if employee.emergency_contact_address:
                    self.emergency_address_text.insert('1.0', employee.emergency_contact_address)
                
                # البيانات المهنية
                self.employee_status_var.set(employee.employee_status or "")
                
                if employee.corps:
                    self.corps_var.set(employee.corps.name_ar)
                    self.on_corps_change()
                
                if employee.current_rank:
                    self.current_rank_var.set(employee.current_rank.name_ar)
                
                if employee.rank_promotion_date:
                    self.rank_promotion_date_var.set(employee.rank_promotion_date.strftime("%d/%m/%Y"))
                
                if employee.current_directorate:
                    self.current_directorate_var.set(employee.current_directorate.name_ar)
                
                if employee.recruitment_date:
                    self.recruitment_date_var.set(employee.recruitment_date.strftime("%d/%m/%Y"))
                
                self.social_security_number_var.set(employee.social_security_number or "")
                self.postal_account_number_var.set(employee.postal_account_number or "")
                self.national_id_number_var.set(employee.national_id_number or "")
                
                # تحميل الصورة إذا كانت موجودة
                if employee.photo_path and os.path.exists(employee.photo_path):
                    try:
                        image = Image.open(employee.photo_path)
                        image = image.resize((150, 200), Image.Resampling.LANCZOS)
                        photo = ImageTk.PhotoImage(image)
                        self.photo_label.config(image=photo, text="")
                        self.photo_label.image = photo
                        self.selected_photo_path = employee.photo_path
                    except Exception as e:
                        print(f"خطأ في تحميل صورة الموظف: {e}")
            
            session.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل بيانات الموظف:\n{str(e)}")
    
    def validate_form(self):
        """التحقق من صحة البيانات المدخلة"""
        errors = []
        
        # التحقق من الحقول المطلوبة
        if not self.registration_number_var.get().strip():
            errors.append("رقم التسجيل مطلوب")
        
        if not self.last_name_ar_var.get().strip():
            errors.append("اللقب مطلوب")
        
        if not self.first_name_ar_var.get().strip():
            errors.append("الاسم مطلوب")
        
        if not self.gender_var.get():
            errors.append("الجنس مطلوب")
        
        if not self.birth_date_var.get().strip():
            errors.append("تاريخ الميلاد مطلوب")
        
        if not self.birth_wilaya_var.get():
            errors.append("ولاية الميلاد مطلوبة")
        
        if not self.birth_commune_var.get():
            errors.append("بلدية الميلاد مطلوبة")
        
        if not self.marital_status_var.get():
            errors.append("الحالة العائلية مطلوبة")
        
        if not self.employee_status_var.get():
            errors.append("حالة الموظف مطلوبة")
        
        if not self.corps_var.get():
            errors.append("السلك مطلوب")
        
        if not self.current_rank_var.get():
            errors.append("الرتبة الحالية مطلوبة")
        
        if not self.current_directorate_var.get():
            errors.append("المديرية مطلوبة")
        
        if not self.recruitment_date_var.get().strip():
            errors.append("تاريخ التوظيف مطلوب")
        
        # التحقق من صحة التواريخ
        try:
            if self.birth_date_var.get().strip():
                birth_date = datetime.strptime(self.birth_date_var.get(), "%d/%m/%Y").date()
                age = (date.today() - birth_date).days // 365
                if age < Config.MIN_AGE or age > Config.MAX_AGE:
                    errors.append(f"العمر يجب أن يكون بين {Config.MIN_AGE} و {Config.MAX_AGE} سنة")
        except ValueError:
            errors.append("تاريخ الميلاد غير صحيح (يوم/شهر/سنة)")
        
        try:
            if self.recruitment_date_var.get().strip():
                datetime.strptime(self.recruitment_date_var.get(), "%d/%m/%Y")
        except ValueError:
            errors.append("تاريخ التوظيف غير صحيح (يوم/شهر/سنة)")
        
        # التحقق من رقم التسجيل (6 أرقام)
        reg_number = self.registration_number_var.get().strip()
        if reg_number and (not reg_number.isdigit() or len(reg_number) != 6):
            errors.append("رقم التسجيل يجب أن يكون 6 أرقام")
        
        # التحقق من البريد الإلكتروني
        email = self.email_var.get().strip()
        if email and not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
            errors.append("البريد الإلكتروني غير صحيح")
        
        return errors
    
    def save_employee(self):
        """حفظ بيانات الموظف"""
        # التحقق من صحة البيانات
        errors = self.validate_form()
        if errors:
            messagebox.showerror("خطأ في البيانات", "\n".join(errors))
            return
        
        try:
            session = self.db_manager.get_session()
            
            # إنشاء أو تحديث الموظف
            if self.is_edit_mode:
                employee = session.query(Employee).get(self.employee_id)
                if not employee:
                    messagebox.showerror("خطأ", "لم يتم العثور على الموظف")
                    return
            else:
                employee = Employee()
            
            # البيانات الأساسية
            employee.registration_number = self.registration_number_var.get().strip()
            employee.last_name_ar = self.last_name_ar_var.get().strip()
            employee.first_name_ar = self.first_name_ar_var.get().strip()
            employee.last_name_fr = self.last_name_fr_var.get().strip()
            employee.first_name_fr = self.first_name_fr_var.get().strip()
            employee.gender = self.gender_var.get()
            
            # تاريخ الميلاد
            if self.birth_date_var.get().strip():
                employee.birth_date = datetime.strptime(self.birth_date_var.get(), "%d/%m/%Y").date()
            
            # ولاية وبلدية الميلاد
            birth_wilaya_name = self.birth_wilaya_var.get()
            birth_wilaya = next((w for w in self.wilayas if w.name_ar == birth_wilaya_name), None)
            if birth_wilaya:
                employee.birth_wilaya_id = birth_wilaya.id
                
                birth_commune_name = self.birth_commune_var.get()
                birth_commune = session.query(Commune).filter_by(
                    name_ar=birth_commune_name,
                    wilaya_id=birth_wilaya.id
                ).first()
                if birth_commune:
                    employee.birth_commune_id = birth_commune.id
            
            employee.marital_status = self.marital_status_var.get()
            employee.children_count = self.children_count_var.get()
            employee.dependents_count = self.dependents_count_var.get()
            employee.blood_type = self.blood_type_var.get()
            
            # بيانات الاتصال
            employee.phone1 = self.phone1_var.get().strip()
            employee.phone2 = self.phone2_var.get().strip()
            employee.email = self.email_var.get().strip()
            employee.main_address = self.main_address_text.get('1.0', tk.END).strip()
            employee.secondary_address = self.secondary_address_text.get('1.0', tk.END).strip()
            employee.emergency_contact_name = self.emergency_contact_name_var.get().strip()
            employee.emergency_contact_address = self.emergency_address_text.get('1.0', tk.END).strip()
            
            # البيانات المهنية
            employee.employee_status = self.employee_status_var.get()
            
            # السلك والرتبة
            corps_name = self.corps_var.get()
            corps = next((c for c in self.corps if c.name_ar == corps_name), None)
            if corps:
                employee.corps_id = corps.id
                
                rank_name = self.current_rank_var.get()
                rank = session.query(Rank).filter_by(name_ar=rank_name, corps_id=corps.id).first()
                if rank:
                    employee.current_rank_id = rank.id
            
            # تاريخ الترقية
            if self.rank_promotion_date_var.get().strip():
                employee.rank_promotion_date = datetime.strptime(self.rank_promotion_date_var.get(), "%d/%m/%Y").date()
            
            # المديرية
            directorate_name = self.current_directorate_var.get()
            directorate = next((d for d in self.directorates if d.name_ar == directorate_name), None)
            if directorate:
                employee.current_directorate_id = directorate.id
            
            # تاريخ التوظيف
            if self.recruitment_date_var.get().strip():
                employee.recruitment_date = datetime.strptime(self.recruitment_date_var.get(), "%d/%m/%Y").date()
            
            employee.social_security_number = self.social_security_number_var.get().strip()
            employee.postal_account_number = self.postal_account_number_var.get().strip()
            employee.national_id_number = self.national_id_number_var.get().strip()
            
            # حفظ الصورة
            if hasattr(self, 'selected_photo_path') and self.selected_photo_path:
                photo_filename = f"employee_{employee.registration_number}.jpg"
                photo_path = os.path.join(Config.IMAGES_DIR, photo_filename)
                
                try:
                    # نسخ الصورة إلى مجلد الصور
                    image = Image.open(self.selected_photo_path)
                    image = image.resize(Config.MAX_IMAGE_SIZE, Image.Resampling.LANCZOS)
                    image.save(photo_path, "JPEG")
                    employee.photo_path = photo_path
                except Exception as e:
                    print(f"خطأ في حفظ الصورة: {e}")
            
            # حفظ في قاعدة البيانات
            if not self.is_edit_mode:
                session.add(employee)
            
            session.commit()
            
            success_message = "تم تحديث بيانات الموظف بنجاح" if self.is_edit_mode else "تم إضافة الموظف بنجاح"
            messagebox.showinfo("نجح", success_message)
            
            session.close()
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ بيانات الموظف:\n{str(e)}")
    
    def delete_employee(self):
        """حذف الموظف"""
        if not self.is_edit_mode:
            return
        
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا الموظف؟"):
            try:
                session = self.db_manager.get_session()
                employee = session.query(Employee).get(self.employee_id)
                if employee:
                    session.delete(employee)
                    session.commit()
                    messagebox.showinfo("نجح", "تم حذف الموظف بنجاح")
                    session.close()
                    self.window.destroy()
                else:
                    messagebox.showerror("خطأ", "لم يتم العثور على الموظف")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الموظف:\n{str(e)}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.window.destroy()
