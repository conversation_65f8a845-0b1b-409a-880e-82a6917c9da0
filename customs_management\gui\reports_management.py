# -*- coding: utf-8 -*-
"""
واجهة إدارة التقارير
Reports Management Interface
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, date
import os

from database.models import Employee, Wilaya, Commune, Corps, Rank, Directorate, Service, Position
from utils.config import Config

class ReportsManagementFrame(ttk.Frame):
    """إطار إدارة التقارير"""
    
    def __init__(self, parent, db_manager):
        super().__init__(parent)
        self.db_manager = db_manager
        
        # إنشاء الواجهة
        self.create_widgets()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إنشاء Notebook للتبويبات
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # تبويب تقارير الموظفين
        self.create_employee_reports_tab()
        
        # تبويب التقارير الإحصائية
        self.create_statistics_reports_tab()
        
        # تبويب تقارير العطل
        self.create_leave_reports_tab()
        
        # تبويب التقارير المخصصة
        self.create_custom_reports_tab()
    
    def create_employee_reports_tab(self):
        """إنشاء تبويب تقارير الموظفين"""
        self.employee_reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.employee_reports_frame, text="تقارير الموظفين")
        
        # إطار الفلاتر
        filters_frame = ttk.LabelFrame(self.employee_reports_frame, text="فلاتر التقرير", padding=10)
        filters_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # فلتر المديرية
        ttk.Label(filters_frame, text="المديرية:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.directorate_var = tk.StringVar()
        self.directorate_combo = ttk.Combobox(
            filters_frame,
            textvariable=self.directorate_var,
            state="readonly",
            width=30
        )
        self.directorate_combo.grid(row=0, column=1, padx=5, pady=2, sticky=tk.W)
        
        # فلتر السلك
        ttk.Label(filters_frame, text="السلك:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        self.corps_var = tk.StringVar()
        self.corps_combo = ttk.Combobox(
            filters_frame,
            textvariable=self.corps_var,
            state="readonly",
            width=25
        )
        self.corps_combo.grid(row=0, column=3, padx=5, pady=2, sticky=tk.W)
        
        # فلتر الرتبة
        ttk.Label(filters_frame, text="الرتبة:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.rank_var = tk.StringVar()
        self.rank_combo = ttk.Combobox(
            filters_frame,
            textvariable=self.rank_var,
            state="readonly",
            width=30
        )
        self.rank_combo.grid(row=1, column=1, padx=5, pady=2, sticky=tk.W)
        
        # فلتر الحالة
        ttk.Label(filters_frame, text="الحالة:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        self.status_var = tk.StringVar()
        self.status_combo = ttk.Combobox(
            filters_frame,
            textvariable=self.status_var,
            values=["الكل", "نشط", "تحويل", "موقف", "استيداع", "منتدب", "متوفي", "مفصول", "مستقيل"],
            state="readonly",
            width=15
        )
        self.status_combo.grid(row=1, column=3, padx=5, pady=2, sticky=tk.W)
        self.status_combo.set("الكل")
        
        # أزرار التقارير
        reports_frame = ttk.LabelFrame(self.employee_reports_frame, text="أنواع التقارير", padding=10)
        reports_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # الصف الأول من الأزرار
        row1_frame = ttk.Frame(reports_frame)
        row1_frame.pack(fill=tk.X, pady=2)
        
        ttk.Button(
            row1_frame,
            text="قائمة الموظفين الشاملة",
            command=self.generate_comprehensive_employee_list
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            row1_frame,
            text="تقرير البيانات الأساسية",
            command=self.generate_basic_data_report
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            row1_frame,
            text="تقرير البيانات المهنية",
            command=self.generate_professional_data_report
        ).pack(side=tk.LEFT, padx=5)
        
        # الصف الثاني من الأزرار
        row2_frame = ttk.Frame(reports_frame)
        row2_frame.pack(fill=tk.X, pady=2)
        
        ttk.Button(
            row2_frame,
            text="تقرير حسب المديرية",
            command=self.generate_directorate_report
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            row2_frame,
            text="تقرير حسب السلك والرتبة",
            command=self.generate_corps_rank_report
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            row2_frame,
            text="تقرير الأعمار",
            command=self.generate_age_report
        ).pack(side=tk.LEFT, padx=5)
        
        # تحميل البيانات المرجعية
        self.load_reference_data_for_reports()
    
    def create_statistics_reports_tab(self):
        """إنشاء تبويب التقارير الإحصائية"""
        self.statistics_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.statistics_frame, text="التقارير الإحصائية")
        
        # إطار الإحصائيات العامة
        general_stats_frame = ttk.LabelFrame(self.statistics_frame, text="الإحصائيات العامة", padding=10)
        general_stats_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # عرض الإحصائيات
        self.stats_text = tk.Text(general_stats_frame, height=15, width=80)
        stats_scrollbar = ttk.Scrollbar(general_stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار الإحصائيات
        stats_buttons_frame = ttk.Frame(self.statistics_frame)
        stats_buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(
            stats_buttons_frame,
            text="تحديث الإحصائيات",
            command=self.update_statistics
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            stats_buttons_frame,
            text="تصدير الإحصائيات",
            command=self.export_statistics
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            stats_buttons_frame,
            text="رسم بياني",
            command=self.generate_charts
        ).pack(side=tk.LEFT, padx=5)
        
        # تحديث الإحصائيات عند التحميل
        self.update_statistics()
    
    def create_leave_reports_tab(self):
        """إنشاء تبويب تقارير العطل"""
        self.leave_reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.leave_reports_frame, text="تقارير العطل")
        
        # إطار فلاتر العطل
        leave_filters_frame = ttk.LabelFrame(self.leave_reports_frame, text="فلاتر تقارير العطل", padding=10)
        leave_filters_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # فلتر السنة
        ttk.Label(leave_filters_frame, text="السنة:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.leave_year_var = tk.StringVar()
        current_year = datetime.now().year
        years = [str(year) for year in range(current_year - 5, current_year + 2)]
        self.leave_year_combo = ttk.Combobox(
            leave_filters_frame,
            textvariable=self.leave_year_var,
            values=years,
            state="readonly",
            width=10
        )
        self.leave_year_combo.grid(row=0, column=1, padx=5, pady=2, sticky=tk.W)
        self.leave_year_combo.set(str(current_year))
        
        # فلتر نوع العطلة
        ttk.Label(leave_filters_frame, text="نوع العطلة:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        self.leave_type_var = tk.StringVar()
        self.leave_type_combo = ttk.Combobox(
            leave_filters_frame,
            textvariable=self.leave_type_var,
            values=["الكل", "عطل سنوية", "عطل مرضية", "عطل أخرى"],
            state="readonly",
            width=15
        )
        self.leave_type_combo.grid(row=0, column=3, padx=5, pady=2, sticky=tk.W)
        self.leave_type_combo.set("الكل")
        
        # أزرار تقارير العطل
        leave_reports_buttons_frame = ttk.LabelFrame(self.leave_reports_frame, text="تقارير العطل", padding=10)
        leave_reports_buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(
            leave_reports_buttons_frame,
            text="تقرير العطل السنوية",
            command=self.generate_annual_leave_report
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            leave_reports_buttons_frame,
            text="تقرير العطل المرضية",
            command=self.generate_sick_leave_report
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            leave_reports_buttons_frame,
            text="تقرير العطل الأخرى",
            command=self.generate_other_leave_report
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            leave_reports_buttons_frame,
            text="تقرير شامل للعطل",
            command=self.generate_comprehensive_leave_report
        ).pack(side=tk.LEFT, padx=5)
    
    def create_custom_reports_tab(self):
        """إنشاء تبويب التقارير المخصصة"""
        self.custom_reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.custom_reports_frame, text="تقارير مخصصة")
        
        # إطار بناء التقرير المخصص
        custom_builder_frame = ttk.LabelFrame(self.custom_reports_frame, text="بناء تقرير مخصص", padding=10)
        custom_builder_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # اختيار الحقول
        fields_frame = ttk.LabelFrame(custom_builder_frame, text="اختيار الحقول", padding=5)
        fields_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # قائمة الحقول المتاحة
        self.available_fields = [
            "رقم التسجيل",
            "الاسم الكامل",
            "الجنس",
            "تاريخ الميلاد",
            "العمر",
            "ولاية الميلاد",
            "بلدية الميلاد",
            "الحالة العائلية",
            "عدد الأبناء",
            "زمرة الدم",
            "رقم الهاتف 1",
            "رقم الهاتف 2",
            "البريد الإلكتروني",
            "العنوان الرئيسي",
            "حالة الموظف",
            "السلك",
            "الرتبة الحالية",
            "الوظيفة الحالية",
            "المديرية",
            "المصلحة",
            "تاريخ التوظيف",
            "سنوات الخدمة",
            "رقم الضمان الاجتماعي",
            "رقم الحساب الجاري البريدي",
            "رقم بطاقة التعريف الوطنية"
        ]
        
        # قائمة الحقول المتاحة
        ttk.Label(fields_frame, text="الحقول المتاحة:").pack(anchor=tk.W)
        self.available_listbox = tk.Listbox(fields_frame, selectmode=tk.MULTIPLE, height=15)
        for field in self.available_fields:
            self.available_listbox.insert(tk.END, field)
        self.available_listbox.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # أزرار النقل
        buttons_frame = ttk.Frame(custom_builder_frame)
        buttons_frame.pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        ttk.Button(
            buttons_frame,
            text=">>",
            command=self.add_fields_to_report
        ).pack(pady=5)
        
        ttk.Button(
            buttons_frame,
            text="<<",
            command=self.remove_fields_from_report
        ).pack(pady=5)
        
        ttk.Button(
            buttons_frame,
            text="الكل >>",
            command=self.add_all_fields_to_report
        ).pack(pady=5)
        
        ttk.Button(
            buttons_frame,
            text="<< مسح الكل",
            command=self.remove_all_fields_from_report
        ).pack(pady=5)
        
        # قائمة الحقول المختارة
        selected_frame = ttk.LabelFrame(custom_builder_frame, text="الحقول المختارة", padding=5)
        selected_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5)
        
        ttk.Label(selected_frame, text="الحقول المختارة للتقرير:").pack(anchor=tk.W)
        self.selected_listbox = tk.Listbox(selected_frame, selectmode=tk.MULTIPLE, height=15)
        self.selected_listbox.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # أزرار التقرير المخصص
        custom_buttons_frame = ttk.Frame(self.custom_reports_frame)
        custom_buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(
            custom_buttons_frame,
            text="معاينة التقرير",
            command=self.preview_custom_report
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            custom_buttons_frame,
            text="تصدير إلى Excel",
            command=self.export_custom_report_excel
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            custom_buttons_frame,
            text="تصدير إلى PDF",
            command=self.export_custom_report_pdf
        ).pack(side=tk.LEFT, padx=5)
    
    def load_reference_data_for_reports(self):
        """تحميل البيانات المرجعية للتقارير"""
        try:
            session = self.db_manager.get_session()
            
            # تحميل المديريات
            directorates = session.query(Directorate).order_by(Directorate.name_ar).all()
            directorate_names = ["الكل"] + [d.name_ar for d in directorates]
            self.directorate_combo['values'] = directorate_names
            self.directorate_combo.set("الكل")
            
            # تحميل الأسلاك
            corps = session.query(Corps).order_by(Corps.name_ar).all()
            corps_names = ["الكل"] + [c.name_ar for c in corps]
            self.corps_combo['values'] = corps_names
            self.corps_combo.set("الكل")
            
            # تحميل الرتب
            ranks = session.query(Rank).order_by(Rank.name_ar).all()
            rank_names = ["الكل"] + [r.name_ar for r in ranks]
            self.rank_combo['values'] = rank_names
            self.rank_combo.set("الكل")
            
            session.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات المرجعية:\n{str(e)}")
    
    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            session = self.db_manager.get_session()
            
            # مسح النص الحالي
            self.stats_text.delete('1.0', tk.END)
            
            # إحصائيات عامة
            total_employees = session.query(Employee).count()
            active_employees = session.query(Employee).filter_by(employee_status='نشط').count()
            
            stats_text = f"الإحصائيات العامة:\n"
            stats_text += f"{'='*50}\n"
            stats_text += f"إجمالي الموظفين: {total_employees}\n"
            stats_text += f"الموظفين النشطين: {active_employees}\n"
            stats_text += f"الموظفين غير النشطين: {total_employees - active_employees}\n\n"
            
            # إحصائيات حسب الحالة
            stats_text += f"إحصائيات حسب حالة الموظف:\n"
            stats_text += f"{'='*50}\n"
            
            statuses = ["نشط", "تحويل", "موقف", "استيداع", "منتدب", "متوفي", "مفصول", "مستقيل"]
            for status in statuses:
                count = session.query(Employee).filter_by(employee_status=status).count()
                if count > 0:
                    percentage = (count / total_employees) * 100 if total_employees > 0 else 0
                    stats_text += f"{status}: {count} ({percentage:.1f}%)\n"
            
            stats_text += "\n"
            
            # إحصائيات حسب الجنس
            male_count = session.query(Employee).filter_by(gender='ذكر').count()
            female_count = session.query(Employee).filter_by(gender='أنثى').count()
            
            stats_text += f"إحصائيات حسب الجنس:\n"
            stats_text += f"{'='*50}\n"
            stats_text += f"ذكور: {male_count} ({(male_count/total_employees)*100:.1f}%)\n" if total_employees > 0 else "ذكور: 0\n"
            stats_text += f"إناث: {female_count} ({(female_count/total_employees)*100:.1f}%)\n" if total_employees > 0 else "إناث: 0\n"
            stats_text += "\n"
            
            # إحصائيات حسب الأسلاك
            stats_text += f"إحصائيات حسب الأسلاك:\n"
            stats_text += f"{'='*50}\n"
            
            corps = session.query(Corps).all()
            for corp in corps:
                count = session.query(Employee).filter_by(corps_id=corp.id).count()
                if count > 0:
                    percentage = (count / total_employees) * 100 if total_employees > 0 else 0
                    stats_text += f"{corp.name_ar}: {count} ({percentage:.1f}%)\n"
            
            stats_text += "\n"
            
            # إحصائيات حسب المديريات
            stats_text += f"إحصائيات حسب المديريات:\n"
            stats_text += f"{'='*50}\n"
            
            directorates = session.query(Directorate).all()
            for directorate in directorates:
                count = session.query(Employee).filter_by(current_directorate_id=directorate.id).count()
                if count > 0:
                    percentage = (count / total_employees) * 100 if total_employees > 0 else 0
                    stats_text += f"{directorate.name_ar}: {count} ({percentage:.1f}%)\n"
            
            # عرض الإحصائيات
            self.stats_text.insert('1.0', stats_text)
            
            session.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث الإحصائيات:\n{str(e)}")
    
    # دوال التقارير
    def generate_comprehensive_employee_list(self):
        """إنتاج قائمة الموظفين الشاملة"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def generate_basic_data_report(self):
        """إنتاج تقرير البيانات الأساسية"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def generate_professional_data_report(self):
        """إنتاج تقرير البيانات المهنية"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def generate_directorate_report(self):
        """إنتاج تقرير حسب المديرية"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def generate_corps_rank_report(self):
        """إنتاج تقرير حسب السلك والرتبة"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def generate_age_report(self):
        """إنتاج تقرير الأعمار"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def generate_annual_leave_report(self):
        """إنتاج تقرير العطل السنوية"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def generate_sick_leave_report(self):
        """إنتاج تقرير العطل المرضية"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def generate_other_leave_report(self):
        """إنتاج تقرير العطل الأخرى"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def generate_comprehensive_leave_report(self):
        """إنتاج تقرير شامل للعطل"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def export_statistics(self):
        """تصدير الإحصائيات"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def generate_charts(self):
        """إنتاج الرسوم البيانية"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    # دوال التقرير المخصص
    def add_fields_to_report(self):
        """إضافة حقول إلى التقرير"""
        selected_indices = self.available_listbox.curselection()
        for index in reversed(selected_indices):
            field = self.available_listbox.get(index)
            self.selected_listbox.insert(tk.END, field)
            self.available_listbox.delete(index)
    
    def remove_fields_from_report(self):
        """إزالة حقول من التقرير"""
        selected_indices = self.selected_listbox.curselection()
        for index in reversed(selected_indices):
            field = self.selected_listbox.get(index)
            self.available_listbox.insert(tk.END, field)
            self.selected_listbox.delete(index)
    
    def add_all_fields_to_report(self):
        """إضافة جميع الحقول إلى التقرير"""
        for i in range(self.available_listbox.size()):
            field = self.available_listbox.get(0)
            self.selected_listbox.insert(tk.END, field)
            self.available_listbox.delete(0)
    
    def remove_all_fields_from_report(self):
        """إزالة جميع الحقول من التقرير"""
        for i in range(self.selected_listbox.size()):
            field = self.selected_listbox.get(0)
            self.available_listbox.insert(tk.END, field)
            self.selected_listbox.delete(0)
    
    def preview_custom_report(self):
        """معاينة التقرير المخصص"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def export_custom_report_excel(self):
        """تصدير التقرير المخصص إلى Excel"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def export_custom_report_pdf(self):
        """تصدير التقرير المخصص إلى PDF"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
