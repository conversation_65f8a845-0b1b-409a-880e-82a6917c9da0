/**
 * ملف JavaScript مخصص لنظام إدارة موظفي الجمارك
 * Custom JavaScript for Customs Personnel Management System
 */

$(document).ready(function() {
    // تهيئة التطبيق
    initializeApp();
    
    // تهيئة DataTables
    initializeDataTables();
    
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة التنبيهات
    initializeAlerts();
    
    // تهيئة الأحداث
    initializeEvents();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // إضافة تأثيرات الانتقال
    $('body').addClass('fade-in');
    
    // تهيئة tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تهيئة popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // إضافة تأثير loading للروابط
    $('a[href]:not([href="#"]):not([data-bs-toggle])').on('click', function() {
        showLoading();
    });
}

/**
 * تهيئة DataTables
 */
function initializeDataTables() {
    if ($.fn.DataTable) {
        $('.data-table').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
            },
            responsive: true,
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
            drawCallback: function() {
                // إعادة تهيئة tooltips بعد إعادة رسم الجدول
                $('[data-bs-toggle="tooltip"]').tooltip();
            }
        });
    }
}

/**
 * تهيئة النماذج
 */
function initializeForms() {
    // التحقق من صحة النماذج
    $('form').on('submit', function(e) {
        if (!validateForm(this)) {
            e.preventDefault();
            return false;
        }
        
        // إضافة تأثير loading
        const submitBtn = $(this).find('button[type="submit"], input[type="submit"]');
        addLoadingToButton(submitBtn);
    });
    
    // تحسين حقول الإدخال
    $('.form-control, .form-select').on('focus', function() {
        $(this).closest('.form-group, .mb-3').addClass('focused');
    }).on('blur', function() {
        if (!$(this).val()) {
            $(this).closest('.form-group, .mb-3').removeClass('focused');
        }
    });
    
    // تنسيق أرقام الهاتف
    $('input[type="tel"]').on('input', function() {
        formatPhoneNumber(this);
    });
    
    // التحقق من البريد الإلكتروني
    $('input[type="email"]').on('blur', function() {
        validateEmail(this);
    });
    
    // تحديد الكل/إلغاء التحديد
    $('.select-all').on('change', function() {
        const isChecked = $(this).is(':checked');
        $(this).closest('table').find('tbody input[type="checkbox"]').prop('checked', isChecked);
    });
}

/**
 * تهيئة التنبيهات
 */
function initializeAlerts() {
    // إخفاء التنبيهات تلقائياً
    $('.alert:not(.alert-permanent)').delay(5000).fadeOut();
    
    // تأكيد الحذف
    $('.btn-delete, .delete-btn').on('click', function(e) {
        e.preventDefault();
        const url = $(this).attr('href') || $(this).data('url');
        const title = $(this).data('title') || 'تأكيد الحذف';
        const text = $(this).data('text') || 'هل أنت متأكد من أنك تريد حذف هذا العنصر؟';
        
        confirmDelete(url, title, text);
    });
}

/**
 * تهيئة الأحداث
 */
function initializeEvents() {
    // البحث المباشر
    $('.search-input').on('keyup', debounce(function() {
        const searchTerm = $(this).val();
        performSearch(searchTerm);
    }, 300));
    
    // تحديث الصفحة
    $('.refresh-btn').on('click', function() {
        location.reload();
    });
    
    // طباعة الصفحة
    $('.print-btn').on('click', function() {
        window.print();
    });
    
    // تصدير البيانات
    $('.export-btn').on('click', function() {
        const format = $(this).data('format') || 'excel';
        const url = $(this).data('url');
        exportData(url, format);
    });
}

/**
 * التحقق من صحة النموذج
 */
function validateForm(form) {
    let isValid = true;
    const $form = $(form);
    
    // إزالة رسائل الخطأ السابقة
    $form.find('.is-invalid').removeClass('is-invalid');
    $form.find('.invalid-feedback').remove();
    
    // التحقق من الحقول المطلوبة
    $form.find('[required]').each(function() {
        if (!$(this).val().trim()) {
            isValid = false;
            $(this).addClass('is-invalid');
            $(this).after('<div class="invalid-feedback">هذا الحقل مطلوب</div>');
        }
    });
    
    // التحقق من البريد الإلكتروني
    $form.find('input[type="email"]').each(function() {
        if ($(this).val() && !isValidEmail($(this).val())) {
            isValid = false;
            $(this).addClass('is-invalid');
            $(this).after('<div class="invalid-feedback">البريد الإلكتروني غير صحيح</div>');
        }
    });
    
    // التحقق من أرقام الهاتف
    $form.find('input[type="tel"]').each(function() {
        if ($(this).val() && !isValidPhoneNumber($(this).val())) {
            isValid = false;
            $(this).addClass('is-invalid');
            $(this).after('<div class="invalid-feedback">رقم الهاتف غير صحيح</div>');
        }
    });
    
    return isValid;
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * التحقق من صحة رقم الهاتف الجزائري
 */
function isValidPhoneNumber(phone) {
    const phoneRegex = /^(0[5-7]\d{8}|0\d{8})$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
}

/**
 * تنسيق رقم الهاتف
 */
function formatPhoneNumber(input) {
    let value = input.value.replace(/\D/g, '');
    
    if (value.length >= 10) {
        if (value.startsWith('05') || value.startsWith('06') || value.startsWith('07')) {
            // هاتف محمول
            value = value.replace(/(\d{4})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4');
        } else if (value.startsWith('0')) {
            // هاتف ثابت
            value = value.replace(/(\d{3})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4');
        }
    }
    
    input.value = value;
}

/**
 * تأكيد الحذف
 */
function confirmDelete(url, title, text) {
    Swal.fire({
        title: title,
        text: text,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            if (url) {
                window.location.href = url;
            } else {
                // إرسال نموذج الحذف
                $('#delete-form').submit();
            }
        }
    });
}

/**
 * عرض رسالة نجاح
 */
function showSuccess(message, title = 'نجح') {
    Swal.fire({
        icon: 'success',
        title: title,
        text: message,
        confirmButtonText: 'موافق'
    });
}

/**
 * عرض رسالة خطأ
 */
function showError(message, title = 'خطأ') {
    Swal.fire({
        icon: 'error',
        title: title,
        text: message,
        confirmButtonText: 'موافق'
    });
}

/**
 * عرض شاشة التحميل
 */
function showLoading(message = 'جاري التحميل...') {
    Swal.fire({
        title: message,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
}

/**
 * إخفاء شاشة التحميل
 */
function hideLoading() {
    Swal.close();
}

/**
 * إضافة تأثير loading للزر
 */
function addLoadingToButton(button) {
    const $btn = $(button);
    const originalText = $btn.text();
    
    $btn.prop('disabled', true);
    $btn.html('<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...');
    
    // إعادة تعيين الزر بعد 10 ثوان (في حالة عدم إعادة تحميل الصفحة)
    setTimeout(() => {
        $btn.prop('disabled', false);
        $btn.html(originalText);
    }, 10000);
}

/**
 * البحث المباشر
 */
function performSearch(searchTerm) {
    // تنفيذ البحث حسب الصفحة الحالية
    if ($('.data-table').length) {
        $('.data-table').DataTable().search(searchTerm).draw();
    }
}

/**
 * تصدير البيانات
 */
function exportData(url, format) {
    showLoading('جاري تصدير البيانات...');
    
    // إنشاء رابط تحميل مخفي
    const link = document.createElement('a');
    link.href = url + '?format=' + format;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    setTimeout(() => {
        hideLoading();
    }, 2000);
}

/**
 * دالة debounce لتحسين الأداء
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * تحديث الإحصائيات في الوقت الفعلي
 */
function updateStats() {
    fetch('/api/dashboard-stats')
        .then(response => response.json())
        .then(data => {
            // تحديث الإحصائيات في الواجهة
            $('.stats-employees-total').text(data.employees.total);
            $('.stats-employees-active').text(data.employees.active);
            $('.stats-leaves-total').text(data.leaves.total);
            $('.stats-users-total').text(data.users.total);
        })
        .catch(error => {
            console.error('خطأ في تحديث الإحصائيات:', error);
        });
}

/**
 * تحديث الإحصائيات كل 5 دقائق
 */
setInterval(updateStats, 300000);

/**
 * معالجة الأخطاء العامة
 */
window.addEventListener('error', function(e) {
    console.error('خطأ في JavaScript:', e.error);
});

/**
 * معالجة الأخطاء في AJAX
 */
$(document).ajaxError(function(event, xhr, settings, thrownError) {
    if (xhr.status === 403) {
        showError('ليس لديك صلاحية للوصول إلى هذا المورد');
    } else if (xhr.status === 404) {
        showError('المورد المطلوب غير موجود');
    } else if (xhr.status === 500) {
        showError('حدث خطأ في الخادم. يرجى المحاولة مرة أخرى');
    } else {
        showError('حدث خطأ غير متوقع');
    }
});

/**
 * حفظ حالة الشريط الجانبي
 */
function toggleSidebar() {
    $('.sidebar').toggleClass('show');
    localStorage.setItem('sidebarState', $('.sidebar').hasClass('show'));
}

/**
 * استعادة حالة الشريط الجانبي
 */
function restoreSidebarState() {
    const sidebarState = localStorage.getItem('sidebarState');
    if (sidebarState === 'true') {
        $('.sidebar').addClass('show');
    }
}

// استعادة حالة الشريط الجانبي عند تحميل الصفحة
$(document).ready(function() {
    restoreSidebarState();
});

/**
 * تحسينات إضافية للواجهة
 */
$(document).ready(function() {
    // إضافة تأثير hover للبطاقات
    $('.card').hover(
        function() { $(this).addClass('shadow-lg'); },
        function() { $(this).removeClass('shadow-lg'); }
    );
    
    // تحسين القوائم المنسدلة
    $('.dropdown-toggle').dropdown();
    
    // إضافة تأثيرات للأزرار
    $('.btn').on('click', function() {
        $(this).addClass('clicked');
        setTimeout(() => {
            $(this).removeClass('clicked');
        }, 150);
    });
});
