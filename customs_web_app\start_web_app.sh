#!/bin/bash
# -*- coding: utf-8 -*-

# تطبيق الويب لتسيير مستخدمي الجمارك الجزائرية
# Algerian Customs Personnel Management Web Application

# الألوان
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# دالة طباعة ملونة
print_colored() {
    echo -e "${2}${1}${NC}"
}

# دالة التحقق من الأخطاء
check_error() {
    if [ $? -ne 0 ]; then
        print_colored "❌ $1" $RED
        exit 1
    fi
}

# عنوان التطبيق
echo "========================================"
print_colored "🌐 تطبيق الويب لتسيير مستخدمي الجمارك" $CYAN
print_colored "🌐 Customs Web Application" $CYAN
echo "========================================"
echo

# التحقق من Python
print_colored "🔍 التحقق من Python..." $YELLOW
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        print_colored "❌ Python غير مثبت على النظام" $RED
        echo "يرجى تثبيت Python 3.8 أو أحدث"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# التحقق من إصدار Python
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | awk '{print $2}')
print_colored "✅ تم العثور على Python $PYTHON_VERSION" $GREEN

# التحقق من pip
print_colored "🔍 التحقق من pip..." $YELLOW
if ! command -v pip3 &> /dev/null; then
    if ! command -v pip &> /dev/null; then
        print_colored "❌ pip غير مثبت" $RED
        echo "يرجى تثبيت pip"
        exit 1
    else
        PIP_CMD="pip"
    fi
else
    PIP_CMD="pip3"
fi

print_colored "✅ تم العثور على pip" $GREEN

# تثبيت المتطلبات
print_colored "📦 تثبيت المتطلبات..." $YELLOW
$PIP_CMD install -r requirements.txt > /dev/null 2>&1
if [ $? -ne 0 ]; then
    print_colored "⚠️ محاولة تثبيت المتطلبات مع --user..." $YELLOW
    $PIP_CMD install --user -r requirements.txt
    check_error "فشل في تثبيت المتطلبات"
fi

print_colored "✅ تم تثبيت المتطلبات بنجاح" $GREEN

# إعداد البيئة
print_colored "⚙️ إعداد البيئة..." $YELLOW

# إنشاء ملف .env
if [ ! -f .env ]; then
    if [ -f .env.example ]; then
        cp .env.example .env
        print_colored "✅ تم إنشاء ملف .env" $GREEN
    else
        cat > .env << EOF
FLASK_CONFIG=development
FLASK_ENV=development
FLASK_DEBUG=1
SECRET_KEY=dev-secret-key-change-in-production
DEV_DATABASE_URL=sqlite:///customs_dev.db
EOF
        print_colored "✅ تم إنشاء ملف .env أساسي" $GREEN
    fi
fi

# إنشاء المجلدات المطلوبة
mkdir -p app/static/uploads
mkdir -p logs
print_colored "✅ تم إنشاء المجلدات المطلوبة" $GREEN

# تهيئة قاعدة البيانات
print_colored "🗄️ تهيئة قاعدة البيانات..." $YELLOW

# تشغيل سكريبت الإعداد
export FLASK_CONFIG=development
$PYTHON_CMD -c "
import os
import sys
sys.path.insert(0, '.')

try:
    from app import create_app, db
    from app.models import User, Wilaya, Corps
    
    app = create_app()
    with app.app_context():
        # إنشاء الجداول
        db.create_all()
        
        # إنشاء مستخدم مدير إذا لم يكن موجوداً
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                email='<EMAIL>',
                first_name='مدير',
                last_name='النظام',
                is_admin=True
            )
            admin.set_password('admin123')
            db.session.add(admin)
            
            # إضافة بعض الولايات
            wilayas = [
                ('01', 'أدرار', 'Adrar'),
                ('16', 'الجزائر', 'Alger'),
                ('31', 'وهران', 'Oran'),
                ('25', 'قسنطينة', 'Constantine')
            ]
            
            for code, name_ar, name_fr in wilayas:
                if not Wilaya.query.filter_by(code=code).first():
                    wilaya = Wilaya(code=code, name_ar=name_ar, name_fr=name_fr)
                    db.session.add(wilaya)
            
            # إضافة بعض الأسلاك
            corps_data = [
                ('سلك المفتشين الجمركيين', 'Corps des Inspecteurs des Douanes'),
                ('سلك المراقبين الجمركيين', 'Corps des Contrôleurs des Douanes'),
                ('سلك أعوان الجمارك', 'Corps des Agents des Douanes')
            ]
            
            for name_ar, name_fr in corps_data:
                if not Corps.query.filter_by(name_ar=name_ar).first():
                    corps = Corps(name_ar=name_ar, name_fr=name_fr)
                    db.session.add(corps)
            
            db.session.commit()
            print('تم إدراج البيانات الأولية')
        
        print('تم إعداد قاعدة البيانات بنجاح')
        
except Exception as e:
    print(f'خطأ في إعداد قاعدة البيانات: {e}')
    sys.exit(1)
"

check_error "فشل في تهيئة قاعدة البيانات"
print_colored "✅ تم تهيئة قاعدة البيانات بنجاح" $GREEN

echo
echo "========================================"
print_colored "🎉 التطبيق جاهز للتشغيل!" $GREEN
echo "========================================"
echo
print_colored "📍 الرابط: http://localhost:5000" $CYAN
print_colored "👤 اسم المستخدم: admin" $BLUE
print_colored "🔑 كلمة المرور: admin123" $BLUE
echo
print_colored "⚠️ تذكر تغيير كلمة المرور بعد أول تسجيل دخول!" $YELLOW
echo
print_colored "🚀 جاري تشغيل التطبيق..." $GREEN
print_colored "🛑 لإيقاف التطبيق: اضغط Ctrl+C" $YELLOW
echo "========================================"
echo

# تشغيل التطبيق
export FLASK_CONFIG=development
$PYTHON_CMD app.py

echo
print_colored "🛑 تم إغلاق التطبيق" $YELLOW
