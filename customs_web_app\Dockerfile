# Dockerfile لتطبيق الويب
# Dockerfile for Web Application

FROM python:3.9-slim

# تعيين متغيرات البيئة
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV FLASK_CONFIG=production

# تعيين مجلد العمل
WORKDIR /app

# تثبيت متطلبات النظام
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# نسخ ملف المتطلبات وتثبيتها
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# نسخ ملفات التطبيق
COPY . .

# إنشاء مستخدم غير root
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# إنشاء المجلدات المطلوبة
RUN mkdir -p app/static/uploads logs

# تعريض المنفذ
EXPOSE 5000

# الأمر الافتراضي
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "app:app"]
