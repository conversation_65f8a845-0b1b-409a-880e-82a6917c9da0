# إعدادات التطبيق
# Application Settings

# بيئة التطبيق (development, testing, production)
FLASK_CONFIG=development
FLASK_ENV=development
FLASK_DEBUG=1

# مفتاح الأمان (يجب تغييره في الإنتاج)
SECRET_KEY=your-secret-key-here-change-in-production

# إعدادات قاعدة البيانات
# Database Settings

# PostgreSQL (للإنتاج)
DATABASE_URL=postgresql://customs_user:customs_pass@localhost/customs_db

# SQLite (للتطوير)
DEV_DATABASE_URL=sqlite:///customs_dev.db

# إعدادات Redis
REDIS_URL=redis://localhost:6379/0

# إعدادات البريد الإلكتروني
# Email Settings
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=1
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# إعدادات JWT
JWT_SECRET_KEY=your-jwt-secret-key

# إعدادات التحميل
UPLOAD_FOLDER=app/static/uploads
MAX_CONTENT_LENGTH=16777216

# إعدادات الأمان
SESSION_COOKIE_SECURE=0
SESSION_COOKIE_HTTPONLY=1
SESSION_COOKIE_SAMESITE=Lax

# إعدادات التطوير
FLASK_RUN_HOST=0.0.0.0
FLASK_RUN_PORT=5000
