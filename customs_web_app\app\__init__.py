# -*- coding: utf-8 -*-
"""
تطبيق الويب لتسيير مستخدمي الجمارك الجزائرية
Algerian Customs Personnel Management Web Application
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_mail import Mail
from flask_migrate import Migrate
from flask_wtf.csrf import CSR<PERSON>rotect
from flask_bcrypt import Bcrypt
from config import config

# إنشاء كائنات الإضافات
db = SQLAlchemy()
login_manager = LoginManager()
mail = Mail()
migrate = Migrate()
csrf = CSRFProtect()
bcrypt = Bcrypt()

def create_app(config_name='default'):
    """مصنع التطبيق"""
    app = Flask(__name__)
    
    # تحميل الإعدادات
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # تهيئة الإضافات
    db.init_app(app)
    login_manager.init_app(app)
    mail.init_app(app)
    migrate.init_app(app, db)
    csrf.init_app(app)
    bcrypt.init_app(app)
    
    # إعدادات تسجيل الدخول
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    # تسجيل Blueprints
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.employees import bp as employees_bp
    app.register_blueprint(employees_bp, url_prefix='/employees')
    
    from app.reports import bp as reports_bp
    app.register_blueprint(reports_bp, url_prefix='/reports')
    
    from app.settings import bp as settings_bp
    app.register_blueprint(settings_bp, url_prefix='/settings')
    
    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # معالجات الأخطاء
    @app.errorhandler(404)
    def not_found_error(error):
        from flask import render_template
        return render_template('base.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        from flask import render_template
        db.session.rollback()
        return render_template('base.html'), 500

    @app.errorhandler(403)
    def forbidden_error(error):
        from flask import render_template
        return render_template('base.html'), 403
    
    # دوال المساعدة للقوالب
    @app.context_processor
    def inject_template_vars():
        from datetime import datetime
        return {
            'current_year': datetime.now().year,
            'app_name': 'نظام إدارة موظفي الجمارك'
        }
    
    # فلاتر القوالب
    @app.template_filter('datetime')
    def datetime_filter(value, format='%d/%m/%Y %H:%M'):
        if value is None:
            return ""
        return value.strftime(format)
    
    @app.template_filter('date')
    def date_filter(value, format='%d/%m/%Y'):
        if value is None:
            return ""
        return value.strftime(format)
    
    return app

# استيراد النماذج لضمان إنشاء الجداول
from app import models
