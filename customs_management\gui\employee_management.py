# -*- coding: utf-8 -*-
"""
واجهة إدارة الموظفين
Employee Management Interface
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, date
from PIL import Image, ImageTk
import os

from database.models import Employee, Wilaya, Commune, Corps, Rank, Directorate, Service, Position
from utils.config import Config
from .employee_form import EmployeeFormWindow

class EmployeeManagementFrame(ttk.Frame):
    """إطار إدارة الموظفين"""
    
    def __init__(self, parent, db_manager):
        super().__init__(parent)
        self.db_manager = db_manager
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تحميل البيانات
        self.load_employees()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار البحث والفلترة
        search_frame = ttk.LabelFrame(self, text="البحث والفلترة", padding=10)
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # حقل البحث
        ttk.Label(search_frame, text="البحث:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        self.search_entry.grid(row=0, column=1, padx=5)
        self.search_entry.bind('<KeyRelease>', self.on_search_change)
        
        # فلتر الحالة
        ttk.Label(search_frame, text="الحالة:").grid(row=0, column=2, sticky=tk.W, padx=5)
        self.status_var = tk.StringVar()
        self.status_combo = ttk.Combobox(
            search_frame,
            textvariable=self.status_var,
            values=["الكل", "نشط", "تحويل", "موقف", "استيداع", "منتدب", "متوفي", "مفصول", "مستقيل"],
            state="readonly",
            width=15
        )
        self.status_combo.grid(row=0, column=3, padx=5)
        self.status_combo.set("الكل")
        self.status_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(search_frame)
        buttons_frame.grid(row=0, column=4, padx=20)
        
        ttk.Button(
            buttons_frame,
            text="موظف جديد",
            command=self.new_employee
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame,
            text="تعديل",
            command=self.edit_employee
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame,
            text="حذف",
            command=self.delete_employee
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame,
            text="تحديث",
            command=self.load_employees
        ).pack(side=tk.LEFT, padx=2)
        
        # جدول الموظفين
        self.create_employees_table()
    
    def create_employees_table(self):
        """إنشاء جدول الموظفين"""
        # إطار الجدول
        table_frame = ttk.Frame(self)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # إنشاء Treeview
        columns = (
            "registration_number", "full_name", "rank", "position", 
            "directorate", "status", "phone", "email"
        )
        
        self.employees_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=15
        )
        
        # تعيين عناوين الأعمدة
        self.employees_tree.heading("registration_number", text="رقم التسجيل")
        self.employees_tree.heading("full_name", text="الاسم الكامل")
        self.employees_tree.heading("rank", text="الرتبة")
        self.employees_tree.heading("position", text="الوظيفة")
        self.employees_tree.heading("directorate", text="المديرية")
        self.employees_tree.heading("status", text="الحالة")
        self.employees_tree.heading("phone", text="الهاتف")
        self.employees_tree.heading("email", text="البريد الإلكتروني")
        
        # تعيين عرض الأعمدة
        self.employees_tree.column("registration_number", width=100)
        self.employees_tree.column("full_name", width=200)
        self.employees_tree.column("rank", width=150)
        self.employees_tree.column("position", width=150)
        self.employees_tree.column("directorate", width=200)
        self.employees_tree.column("status", width=80)
        self.employees_tree.column("phone", width=120)
        self.employees_tree.column("email", width=200)
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.employees_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.employees_tree.xview)
        self.employees_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.employees_tree.grid(row=0, column=0, sticky='nsew')
        scrollbar_y.grid(row=0, column=1, sticky='ns')
        scrollbar_x.grid(row=1, column=0, sticky='ew')
        
        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط الأحداث
        self.employees_tree.bind('<Double-1>', self.on_employee_double_click)
        self.employees_tree.bind('<Button-3>', self.show_context_menu)
    
    def load_employees(self):
        """تحميل بيانات الموظفين"""
        try:
            # مسح البيانات الحالية
            for item in self.employees_tree.get_children():
                self.employees_tree.delete(item)
            
            # جلب البيانات من قاعدة البيانات
            session = self.db_manager.get_session()
            
            query = session.query(Employee).join(
                Rank, Employee.current_rank_id == Rank.id, isouter=True
            ).join(
                Position, Employee.current_position_id == Position.id, isouter=True
            ).join(
                Directorate, Employee.current_directorate_id == Directorate.id, isouter=True
            )
            
            # تطبيق الفلاتر
            search_text = self.search_var.get().strip()
            if search_text:
                query = query.filter(
                    (Employee.first_name_ar.contains(search_text)) |
                    (Employee.last_name_ar.contains(search_text)) |
                    (Employee.registration_number.contains(search_text))
                )
            
            status_filter = self.status_var.get()
            if status_filter and status_filter != "الكل":
                query = query.filter(Employee.employee_status == status_filter)
            
            employees = query.all()
            
            # إضافة البيانات إلى الجدول
            for employee in employees:
                full_name = f"{employee.first_name_ar} {employee.last_name_ar}"
                rank_name = employee.current_rank.name_ar if employee.current_rank else ""
                position_name = employee.current_position.name_ar if employee.current_position else ""
                directorate_name = employee.current_directorate.name_ar if employee.current_directorate else ""
                
                self.employees_tree.insert('', 'end', values=(
                    employee.registration_number,
                    full_name,
                    rank_name,
                    position_name,
                    directorate_name,
                    employee.employee_status,
                    employee.phone1 or "",
                    employee.email or ""
                ), tags=(employee.id,))
            
            session.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل بيانات الموظفين:\n{str(e)}")
    
    def on_search_change(self, event=None):
        """عند تغيير نص البحث"""
        self.load_employees()
    
    def on_filter_change(self, event=None):
        """عند تغيير الفلتر"""
        self.load_employees()
    
    def new_employee(self):
        """إضافة موظف جديد"""
        try:
            form_window = EmployeeFormWindow(self, self.db_manager)
            self.wait_window(form_window.window)
            self.load_employees()  # تحديث القائمة بعد الإضافة
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نموذج الموظف الجديد:\n{str(e)}")
    
    def edit_employee(self):
        """تعديل موظف"""
        selected_item = self.employees_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار موظف للتعديل")
            return
        
        try:
            # الحصول على معرف الموظف
            employee_id = self.employees_tree.item(selected_item[0])['tags'][0]
            
            # فتح نموذج التعديل
            form_window = EmployeeFormWindow(self, self.db_manager, employee_id)
            self.wait_window(form_window.window)
            self.load_employees()  # تحديث القائمة بعد التعديل
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تعديل الموظف:\n{str(e)}")
    
    def delete_employee(self):
        """حذف موظف"""
        selected_item = self.employees_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار موظف للحذف")
            return
        
        # تأكيد الحذف
        if not messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا الموظف؟\nهذا الإجراء لا يمكن التراجع عنه."):
            return
        
        try:
            # الحصول على معرف الموظف
            employee_id = self.employees_tree.item(selected_item[0])['tags'][0]
            
            # حذف الموظف من قاعدة البيانات
            session = self.db_manager.get_session()
            employee = session.query(Employee).get(employee_id)
            if employee:
                session.delete(employee)
                session.commit()
                messagebox.showinfo("نجح", "تم حذف الموظف بنجاح")
                self.load_employees()
            else:
                messagebox.showerror("خطأ", "لم يتم العثور على الموظف")
            
            session.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حذف الموظف:\n{str(e)}")
    
    def on_employee_double_click(self, event):
        """عند النقر المزدوج على موظف"""
        self.edit_employee()
    
    def show_context_menu(self, event):
        """عرض القائمة السياقية"""
        # إنشاء القائمة السياقية
        context_menu = tk.Menu(self, tearoff=0)
        context_menu.add_command(label="تعديل", command=self.edit_employee)
        context_menu.add_command(label="حذف", command=self.delete_employee)
        context_menu.add_separator()
        context_menu.add_command(label="عرض التفاصيل", command=self.view_employee_details)
        context_menu.add_command(label="طباعة بطاقة", command=self.print_employee_card)
        
        # عرض القائمة
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()
    
    def view_employee_details(self):
        """عرض تفاصيل الموظف"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def print_employee_card(self):
        """طباعة بطاقة الموظف"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def search_employee(self):
        """التركيز على حقل البحث"""
        self.search_entry.focus_set()
