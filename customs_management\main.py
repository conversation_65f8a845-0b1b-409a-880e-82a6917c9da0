#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج تسيير مستخدمي الجمارك الجزائرية
Algerian Customs Personnel Management System

المطور: نظام إدارة الموظفين
التاريخ: 2025
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    try:
        print("🚀 بدء تشغيل برنامج تسيير مستخدمي الجمارك الجزائرية...")
        print("🚀 Starting Algerian Customs Personnel Management System...")

        # اختبار tkinter أولاً
        print("📋 Testing tkinter...")
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة مؤقتاً
        print("✅ tkinter is working")

        # استيراد الوحدات المطلوبة
        print("📦 Importing modules...")

        try:
            from database.database_manager import DatabaseManager
            print("✅ DatabaseManager imported")
        except ImportError as e:
            print(f"❌ Failed to import DatabaseManager: {e}")
            messagebox.showerror("خطأ في الاستيراد", f"فشل في استيراد مدير قاعدة البيانات:\n{e}")
            return

        try:
            from gui.main_window import MainWindow
            print("✅ MainWindow imported")
        except ImportError as e:
            print(f"❌ Failed to import MainWindow: {e}")
            messagebox.showerror("خطأ في الاستيراد", f"فشل في استيراد النافذة الرئيسية:\n{e}")
            return

        try:
            from utils.config import Config
            print("✅ Config imported")
        except ImportError as e:
            print(f"❌ Failed to import Config: {e}")
            messagebox.showerror("خطأ في الاستيراد", f"فشل في استيراد الإعدادات:\n{e}")
            return

        # إنشاء قاعدة البيانات
        print("🗄️ Setting up database...")
        try:
            db_manager = DatabaseManager()
            db_manager.create_tables()
            print("✅ Database setup complete")
        except Exception as e:
            print(f"❌ Database setup failed: {e}")
            messagebox.showerror("خطأ في قاعدة البيانات", f"فشل في إعداد قاعدة البيانات:\n{e}")
            return

        # إنشاء النافذة الرئيسية
        print("🖥️ Creating main window...")
        try:
            root.deiconify()  # إظهار النافذة
            app = MainWindow(root, db_manager)
            print("✅ Main window created successfully")
        except Exception as e:
            print(f"❌ Failed to create main window: {e}")
            messagebox.showerror("خطأ في الواجهة", f"فشل في إنشاء النافذة الرئيسية:\n{e}")
            return

        print("🎉 Application started successfully!")
        print("🎉 تم تشغيل البرنامج بنجاح!")

        # تشغيل التطبيق
        root.mainloop()

    except ImportError as e:
        error_msg = f"خطأ في استيراد المكتبات:\n{str(e)}\n\nيرجى التأكد من تثبيت جميع المتطلبات:\npip install -r requirements.txt"
        print(f"❌ {error_msg}")
        try:
            messagebox.showerror("خطأ في الاستيراد", error_msg)
        except:
            pass
        sys.exit(1)

    except Exception as e:
        error_msg = f"حدث خطأ في تشغيل البرنامج:\n{str(e)}"
        print(f"❌ {error_msg}")
        try:
            messagebox.showerror("خطأ", error_msg)
        except:
            pass
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
