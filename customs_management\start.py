#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط للبرنامج
Simple startup file
"""

import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_python():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} متوفر")
    return True

def check_tkinter():
    """التحقق من tkinter"""
    try:
        import tkinter as tk
        print("✅ tkinter متوفر")
        return True
    except ImportError:
        print("❌ tkinter غير متوفر")
        print("يرجى إعادة تثبيت Python مع تحديد 'tcl/tk and IDLE'")
        return False

def install_requirements():
    """تثبيت المتطلبات الأساسية"""
    requirements = {
        'sqlalchemy': 'sqlalchemy',
        'PIL': 'Pillow',
        'openpyxl': 'openpyxl',
        'dateutil': 'python-dateutil'
    }
    
    for module, package in requirements.items():
        try:
            __import__(module)
            print(f"✅ {module} متوفر")
        except ImportError:
            print(f"⚠️ تثبيت {package}...")
            os.system(f"pip install {package}")

def run_app():
    """تشغيل التطبيق"""
    try:
        print("\n🚀 بدء تشغيل البرنامج...")
        
        # استيراد التطبيق
        import tkinter as tk
        from tkinter import messagebox
        
        # إنشاء نافذة بسيطة أولاً
        root = tk.Tk()
        root.title("برنامج تسيير مستخدمي الجمارك الجزائرية")
        root.geometry("500x400")
        root.configure(bg='lightblue')
        
        # عنوان
        title_label = tk.Label(root, 
                              text="برنامج تسيير مستخدمي الجمارك الجزائرية",
                              font=("Arial", 16, "bold"),
                              bg='lightblue',
                              fg='darkblue')
        title_label.pack(pady=20)
        
        # رسالة ترحيب
        welcome_label = tk.Label(root,
                                text="مرحباً بك في نظام إدارة الموظفين",
                                font=("Arial", 12),
                                bg='lightblue')
        welcome_label.pack(pady=10)
        
        # معلومات النظام
        info_text = f"""
معلومات النظام:
• Python {sys.version_info.major}.{sys.version_info.minor}
• tkinter متوفر
• قاعدة البيانات: SQLite
• الواجهة: عربية/فرنسية
        """
        
        info_label = tk.Label(root,
                             text=info_text,
                             font=("Arial", 10),
                             bg='lightblue',
                             justify='right')
        info_label.pack(pady=20)
        
        # أزرار
        def start_full_app():
            """تشغيل التطبيق الكامل"""
            try:
                root.destroy()
                # استيراد وتشغيل التطبيق الكامل
                exec(open('main.py').read())
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق:\n{e}")
        
        def test_database():
            """اختبار قاعدة البيانات"""
            try:
                from database.database_manager import DatabaseManager
                db = DatabaseManager()
                db.create_tables()
                messagebox.showinfo("نجح", "تم إنشاء قاعدة البيانات بنجاح!")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في قاعدة البيانات:\n{e}")
        
        # إطار الأزرار
        buttons_frame = tk.Frame(root, bg='lightblue')
        buttons_frame.pack(pady=20)
        
        start_button = tk.Button(buttons_frame,
                               text="تشغيل البرنامج الكامل",
                               command=start_full_app,
                               font=("Arial", 12),
                               bg='green',
                               fg='white',
                               padx=20,
                               pady=10)
        start_button.pack(side=tk.LEFT, padx=10)
        
        test_button = tk.Button(buttons_frame,
                              text="اختبار قاعدة البيانات",
                              command=test_database,
                              font=("Arial", 12),
                              bg='blue',
                              fg='white',
                              padx=20,
                              pady=10)
        test_button.pack(side=tk.LEFT, padx=10)
        
        exit_button = tk.Button(buttons_frame,
                              text="خروج",
                              command=root.destroy,
                              font=("Arial", 12),
                              bg='red',
                              fg='white',
                              padx=20,
                              pady=10)
        exit_button.pack(side=tk.LEFT, padx=10)
        
        print("✅ تم إنشاء واجهة البدء بنجاح")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🚀 برنامج تسيير مستخدمي الجمارك الجزائرية")
    print("🚀 Algerian Customs Personnel Management System")
    print("=" * 50)
    
    # التحقق من المتطلبات
    if not check_python():
        input("اضغط Enter للخروج...")
        return
    
    if not check_tkinter():
        input("اضغط Enter للخروج...")
        return
    
    # تثبيت المتطلبات
    install_requirements()
    
    # تشغيل التطبيق
    run_app()

if __name__ == "__main__":
    main()
