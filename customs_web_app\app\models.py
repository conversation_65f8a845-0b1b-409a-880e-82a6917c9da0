# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات لتطبيق الويب
Database Models for Web Application
"""

from datetime import datetime, date
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db, login_manager

# نموذج المستخدم
class User(UserMixin, db.Model):
    """نموذج المستخدمين"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    is_admin = db.Column(db.Boolean, default=False)
    last_login = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    roles = db.relationship('UserRole', back_populates='user', cascade='all, delete-orphan')
    
    def set_password(self, password):
        """تعيين كلمة المرور"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)
    
    def has_role(self, role_name):
        """التحقق من وجود دور معين"""
        return any(ur.role.name == role_name for ur in self.roles)
    
    def has_permission(self, permission_name):
        """التحقق من وجود صلاحية معينة"""
        for user_role in self.roles:
            if any(rp.permission.name == permission_name for rp in user_role.role.permissions):
                return True
        return False
    
    @property
    def full_name(self):
        """الاسم الكامل"""
        return f"{self.first_name} {self.last_name}"
    
    def __repr__(self):
        return f'<User {self.username}>'

@login_manager.user_loader
def load_user(user_id):
    """تحميل المستخدم"""
    return User.query.get(int(user_id))

# نموذج الأدوار
class Role(db.Model):
    """نموذج الأدوار"""
    __tablename__ = 'roles'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(80), unique=True, nullable=False)
    description = db.Column(db.String(255))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    users = db.relationship('UserRole', back_populates='role')
    permissions = db.relationship('RolePermission', back_populates='role', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Role {self.name}>'

# نموذج الصلاحيات
class Permission(db.Model):
    """نموذج الصلاحيات"""
    __tablename__ = 'permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(80), unique=True, nullable=False)
    description = db.Column(db.String(255))
    category = db.Column(db.String(50))  # فئة الصلاحية
    
    # العلاقات
    roles = db.relationship('RolePermission', back_populates='permission')
    
    def __repr__(self):
        return f'<Permission {self.name}>'

# جدول ربط المستخدمين بالأدوار
class UserRole(db.Model):
    """ربط المستخدمين بالأدوار"""
    __tablename__ = 'user_roles'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'), nullable=False)
    assigned_at = db.Column(db.DateTime, default=datetime.utcnow)
    assigned_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # العلاقات
    user = db.relationship('User', back_populates='roles', foreign_keys=[user_id])
    role = db.relationship('Role', back_populates='users')
    assigner = db.relationship('User', foreign_keys=[assigned_by])

# جدول ربط الأدوار بالصلاحيات
class RolePermission(db.Model):
    """ربط الأدوار بالصلاحيات"""
    __tablename__ = 'role_permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'), nullable=False)
    permission_id = db.Column(db.Integer, db.ForeignKey('permissions.id'), nullable=False)
    
    # العلاقات
    role = db.relationship('Role', back_populates='permissions')
    permission = db.relationship('Permission', back_populates='roles')

# نماذج البيانات المرجعية
class Wilaya(db.Model):
    """نموذج الولايات"""
    __tablename__ = 'wilayas'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(2), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    name_fr = db.Column(db.String(100))
    
    # العلاقات
    communes = db.relationship('Commune', back_populates='wilaya', lazy='dynamic')
    
    def __repr__(self):
        return f'<Wilaya {self.name_ar}>'

class Commune(db.Model):
    """نموذج البلديات"""
    __tablename__ = 'communes'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(6), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    name_fr = db.Column(db.String(100))
    wilaya_id = db.Column(db.Integer, db.ForeignKey('wilayas.id'), nullable=False)
    
    # العلاقات
    wilaya = db.relationship('Wilaya', back_populates='communes')
    
    def __repr__(self):
        return f'<Commune {self.name_ar}>'

class Corps(db.Model):
    """نموذج الأسلاك"""
    __tablename__ = 'corps'
    
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(100), nullable=False)
    name_fr = db.Column(db.String(100))
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    
    # العلاقات
    ranks = db.relationship('Rank', back_populates='corps', lazy='dynamic')
    
    def __repr__(self):
        return f'<Corps {self.name_ar}>'

class Rank(db.Model):
    """نموذج الرتب"""
    __tablename__ = 'ranks'
    
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(100), nullable=False)
    name_fr = db.Column(db.String(100))
    corps_id = db.Column(db.Integer, db.ForeignKey('corps.id'), nullable=False)
    level = db.Column(db.Integer)
    is_active = db.Column(db.Boolean, default=True)
    
    # العلاقات
    corps = db.relationship('Corps', back_populates='ranks')
    
    def __repr__(self):
        return f'<Rank {self.name_ar}>'

class Directorate(db.Model):
    """نموذج المديريات"""
    __tablename__ = 'directorates'
    
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(200), nullable=False)
    name_fr = db.Column(db.String(200))
    code = db.Column(db.String(10), unique=True)
    parent_id = db.Column(db.Integer, db.ForeignKey('directorates.id'))
    level = db.Column(db.Integer, default=1)
    is_active = db.Column(db.Boolean, default=True)
    
    # العلاقات
    parent = db.relationship('Directorate', remote_side=[id], backref='children')
    services = db.relationship('Service', back_populates='directorate', lazy='dynamic')
    
    def __repr__(self):
        return f'<Directorate {self.name_ar}>'

class Service(db.Model):
    """نموذج المصالح"""
    __tablename__ = 'services'
    
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(200), nullable=False)
    name_fr = db.Column(db.String(200))
    code = db.Column(db.String(10))
    directorate_id = db.Column(db.Integer, db.ForeignKey('directorates.id'), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    
    # العلاقات
    directorate = db.relationship('Directorate', back_populates='services')
    positions = db.relationship('Position', back_populates='service', lazy='dynamic')
    
    def __repr__(self):
        return f'<Service {self.name_ar}>'

class Position(db.Model):
    """نموذج الوظائف"""
    __tablename__ = 'positions'
    
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(200), nullable=False)
    name_fr = db.Column(db.String(200))
    service_id = db.Column(db.Integer, db.ForeignKey('services.id'), nullable=False)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    
    # العلاقات
    service = db.relationship('Service', back_populates='positions')
    
    def __repr__(self):
        return f'<Position {self.name_ar}>'

# نموذج الموظف الرئيسي
class Employee(db.Model):
    """نموذج الموظفين"""
    __tablename__ = 'employees'

    # البيانات الأساسية
    id = db.Column(db.Integer, primary_key=True)
    registration_number = db.Column(db.String(6), unique=True, nullable=False, index=True)
    last_name_ar = db.Column(db.String(100), nullable=False)
    first_name_ar = db.Column(db.String(100), nullable=False)
    last_name_fr = db.Column(db.String(100))
    first_name_fr = db.Column(db.String(100))
    gender = db.Column(db.String(10), nullable=False)
    birth_date = db.Column(db.Date, nullable=False)
    birth_wilaya_id = db.Column(db.Integer, db.ForeignKey('wilayas.id'))
    birth_commune_id = db.Column(db.Integer, db.ForeignKey('communes.id'))
    marital_status = db.Column(db.String(20), nullable=False)
    children_count = db.Column(db.Integer, default=0)
    dependents_count = db.Column(db.Integer, default=0)
    blood_type = db.Column(db.String(5))
    photo_filename = db.Column(db.String(255))

    # بيانات الاتصال
    phone1 = db.Column(db.String(20))
    phone2 = db.Column(db.String(20))
    email = db.Column(db.String(100))
    main_address = db.Column(db.Text)
    secondary_address = db.Column(db.Text)
    emergency_contact_name = db.Column(db.String(200))
    emergency_contact_address = db.Column(db.Text)

    # البيانات المهنية
    employee_status = db.Column(db.String(20), default='نشط')
    corps_id = db.Column(db.Integer, db.ForeignKey('corps.id'))
    current_rank_id = db.Column(db.Integer, db.ForeignKey('ranks.id'))
    rank_promotion_date = db.Column(db.Date)
    current_position_id = db.Column(db.Integer, db.ForeignKey('positions.id'))
    position_assignment_date = db.Column(db.Date)
    current_directorate_id = db.Column(db.Integer, db.ForeignKey('directorates.id'))
    current_service_id = db.Column(db.Integer, db.ForeignKey('services.id'))
    recruitment_date = db.Column(db.Date)
    recruitment_rank_id = db.Column(db.Integer, db.ForeignKey('ranks.id'))

    # أرقام الوثائق
    social_security_number = db.Column(db.String(15))
    postal_account_number = db.Column(db.String(20))
    professional_card_number = db.Column(db.String(20))
    professional_card_issue_date = db.Column(db.Date)
    national_id_number = db.Column(db.String(18))
    national_id_issue_date = db.Column(db.Date)
    national_id_issue_place_id = db.Column(db.Integer, db.ForeignKey('communes.id'))
    driving_license_number = db.Column(db.String(20))
    driving_license_category = db.Column(db.String(10))
    driving_license_issue_date = db.Column(db.Date)
    driving_license_issue_place_id = db.Column(db.Integer, db.ForeignKey('communes.id'))
    mutual_card_number = db.Column(db.String(20))
    mutual_card_issue_date = db.Column(db.Date)
    practiced_sport = db.Column(db.String(100))

    # بيانات النظام
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # العلاقات
    birth_wilaya = db.relationship('Wilaya', foreign_keys=[birth_wilaya_id])
    birth_commune = db.relationship('Commune', foreign_keys=[birth_commune_id])
    corps = db.relationship('Corps')
    current_rank = db.relationship('Rank', foreign_keys=[current_rank_id])
    recruitment_rank = db.relationship('Rank', foreign_keys=[recruitment_rank_id])
    current_position = db.relationship('Position')
    current_directorate = db.relationship('Directorate')
    current_service = db.relationship('Service')
    national_id_issue_place = db.relationship('Commune', foreign_keys=[national_id_issue_place_id])
    driving_license_issue_place = db.relationship('Commune', foreign_keys=[driving_license_issue_place_id])
    creator = db.relationship('User', foreign_keys=[created_by])
    updater = db.relationship('User', foreign_keys=[updated_by])

    # الملفات الفرعية
    certificates = db.relationship('Certificate', back_populates='employee', lazy='dynamic')
    trainings = db.relationship('Training', back_populates='employee', lazy='dynamic')
    languages = db.relationship('EmployeeLanguage', back_populates='employee', lazy='dynamic')
    transfers = db.relationship('Transfer', back_populates='employee', lazy='dynamic')
    annual_leaves = db.relationship('AnnualLeave', back_populates='employee', lazy='dynamic')
    sick_leaves = db.relationship('SickLeave', back_populates='employee', lazy='dynamic')
    other_leaves = db.relationship('OtherLeave', back_populates='employee', lazy='dynamic')

    @property
    def full_name_ar(self):
        """الاسم الكامل بالعربية"""
        return f"{self.first_name_ar} {self.last_name_ar}"

    @property
    def full_name_fr(self):
        """الاسم الكامل بالفرنسية"""
        if self.first_name_fr and self.last_name_fr:
            return f"{self.first_name_fr} {self.last_name_fr}"
        return self.full_name_ar

    @property
    def age(self):
        """العمر"""
        if self.birth_date:
            today = date.today()
            return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))
        return None

    @property
    def service_years(self):
        """سنوات الخدمة"""
        if self.recruitment_date:
            today = date.today()
            return today.year - self.recruitment_date.year
        return None

    def __repr__(self):
        return f'<Employee {self.registration_number}: {self.full_name_ar}>'

# نماذج الملفات الفرعية
class Certificate(db.Model):
    """نموذج الشهادات"""
    __tablename__ = 'certificates'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    certificate_type = db.Column(db.String(100), nullable=False)
    specialization = db.Column(db.String(200))
    grant_year = db.Column(db.Integer)
    granting_institution = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    employee = db.relationship('Employee', back_populates='certificates')

    def __repr__(self):
        return f'<Certificate {self.certificate_type}>'

class Training(db.Model):
    """نموذج التكوين"""
    __tablename__ = 'trainings'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    training_subject = db.Column(db.String(200), nullable=False)
    duration = db.Column(db.String(50))
    start_date = db.Column(db.Date)
    end_date = db.Column(db.Date)
    training_institution = db.Column(db.String(200))
    certificate_obtained = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    employee = db.relationship('Employee', back_populates='trainings')

    def __repr__(self):
        return f'<Training {self.training_subject}>'

class Language(db.Model):
    """نموذج اللغات"""
    __tablename__ = 'languages'

    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(50), nullable=False, unique=True)
    name_fr = db.Column(db.String(50))
    is_active = db.Column(db.Boolean, default=True)

    def __repr__(self):
        return f'<Language {self.name_ar}>'

class EmployeeLanguage(db.Model):
    """نموذج لغات الموظف"""
    __tablename__ = 'employee_languages'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    language_id = db.Column(db.Integer, db.ForeignKey('languages.id'), nullable=False)
    can_write = db.Column(db.Boolean, default=False)
    writing_level = db.Column(db.String(20))  # ممتاز، جيد، متوسط
    can_read = db.Column(db.Boolean, default=False)
    reading_level = db.Column(db.String(20))
    can_speak = db.Column(db.Boolean, default=False)
    speaking_level = db.Column(db.String(20))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    employee = db.relationship('Employee', back_populates='languages')
    language = db.relationship('Language')

    def __repr__(self):
        return f'<EmployeeLanguage {self.language.name_ar}>'

class Transfer(db.Model):
    """نموذج التحويلات والتنقلات"""
    __tablename__ = 'transfers'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    from_directorate_id = db.Column(db.Integer, db.ForeignKey('directorates.id'))
    to_directorate_id = db.Column(db.Integer, db.ForeignKey('directorates.id'))
    from_service_id = db.Column(db.Integer, db.ForeignKey('services.id'))
    to_service_id = db.Column(db.Integer, db.ForeignKey('services.id'))
    from_position_id = db.Column(db.Integer, db.ForeignKey('positions.id'))
    to_position_id = db.Column(db.Integer, db.ForeignKey('positions.id'))
    installation_date = db.Column(db.Date)
    end_date = db.Column(db.Date)
    decision_number = db.Column(db.String(50))
    decision_date = db.Column(db.Date)
    reason = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    employee = db.relationship('Employee', back_populates='transfers')
    from_directorate = db.relationship('Directorate', foreign_keys=[from_directorate_id])
    to_directorate = db.relationship('Directorate', foreign_keys=[to_directorate_id])
    from_service = db.relationship('Service', foreign_keys=[from_service_id])
    to_service = db.relationship('Service', foreign_keys=[to_service_id])
    from_position = db.relationship('Position', foreign_keys=[from_position_id])
    to_position = db.relationship('Position', foreign_keys=[to_position_id])

    @property
    def duration_days(self):
        """مدة التحويل بالأيام"""
        if self.installation_date and self.end_date:
            return (self.end_date - self.installation_date).days
        return None

    def __repr__(self):
        return f'<Transfer {self.employee.registration_number}>'

class AnnualLeave(db.Model):
    """نموذج العطل السنوية"""
    __tablename__ = 'annual_leaves'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    leave_year = db.Column(db.Integer, nullable=False)
    days_count = db.Column(db.Integer, nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date)
    destination = db.Column(db.String(200))
    decision_number = db.Column(db.String(50))
    decision_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='مطلوبة')  # مطلوبة، موافق عليها، مرفوضة، منتهية
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    employee = db.relationship('Employee', back_populates='annual_leaves')

    def __repr__(self):
        return f'<AnnualLeave {self.employee.registration_number} - {self.leave_year}>'

class SickLeave(db.Model):
    """نموذج العطل المرضية"""
    __tablename__ = 'sick_leaves'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    leave_type = db.Column(db.String(100), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    days_count = db.Column(db.Integer, nullable=False)
    end_date = db.Column(db.Date)
    is_indexed = db.Column(db.Boolean, default=False)
    internal_medical_control = db.Column(db.Boolean, default=False)
    consultant_doctor_opinion = db.Column(db.String(20))
    medical_certificate_path = db.Column(db.String(255))
    deduction_number = db.Column(db.String(50))
    deduction_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='مطلوبة')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    employee = db.relationship('Employee', back_populates='sick_leaves')

    def __repr__(self):
        return f'<SickLeave {self.employee.registration_number}>'

class OtherLeave(db.Model):
    """نموذج العطل الأخرى"""
    __tablename__ = 'other_leaves'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    leave_type = db.Column(db.String(100), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    days_count = db.Column(db.Integer, nullable=False)
    end_date = db.Column(db.Date)
    reason = db.Column(db.Text)
    destination = db.Column(db.String(200))
    decision_number = db.Column(db.String(50))
    decision_date = db.Column(db.Date)
    salary_deduction = db.Column(db.Boolean, default=False)
    deduction_number = db.Column(db.String(50))
    deduction_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='مطلوبة')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    employee = db.relationship('Employee', back_populates='other_leaves')

    def __repr__(self):
        return f'<OtherLeave {self.employee.registration_number}>'
