# -*- coding: utf-8 -*-
"""
نماذج المصادقة والتسجيل
Authentication and Registration Forms
"""

from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError
from app.models import User

class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    username = StringField('اسم المستخدم', validators=[DataRequired()], 
                          render_kw={"placeholder": "أدخل اسم المستخدم"})
    password = PasswordField('كلمة المرور', validators=[DataRequired()],
                           render_kw={"placeholder": "أدخل كلمة المرور"})
    remember_me = BooleanField('تذكرني')
    submit = SubmitField('تسجيل الدخول')

class RegistrationForm(FlaskForm):
    """نموذج التسجيل"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=4, max=20)],
                          render_kw={"placeholder": "اختر اسم مستخدم"})
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()],
                       render_kw={"placeholder": "أدخل البريد الإلكتروني"})
    first_name = StringField('الاسم الأول', validators=[DataRequired(), Length(max=100)],
                           render_kw={"placeholder": "الاسم الأول"})
    last_name = StringField('اسم العائلة', validators=[DataRequired(), Length(max=100)],
                          render_kw={"placeholder": "اسم العائلة"})
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=6)],
                           render_kw={"placeholder": "اختر كلمة مرور قوية"})
    password2 = PasswordField('تأكيد كلمة المرور', 
                            validators=[DataRequired(), EqualTo('password')],
                            render_kw={"placeholder": "أعد إدخال كلمة المرور"})
    submit = SubmitField('إنشاء الحساب')
    
    def validate_username(self, username):
        """التحقق من عدم وجود اسم المستخدم"""
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('اسم المستخدم موجود بالفعل. يرجى اختيار اسم آخر.')
    
    def validate_email(self, email):
        """التحقق من عدم وجود البريد الإلكتروني"""
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError('البريد الإلكتروني مستخدم بالفعل. يرجى اختيار بريد آخر.')

class ChangePasswordForm(FlaskForm):
    """نموذج تغيير كلمة المرور"""
    old_password = PasswordField('كلمة المرور الحالية', validators=[DataRequired()],
                               render_kw={"placeholder": "أدخل كلمة المرور الحالية"})
    new_password = PasswordField('كلمة المرور الجديدة', 
                               validators=[DataRequired(), Length(min=6)],
                               render_kw={"placeholder": "أدخل كلمة المرور الجديدة"})
    new_password2 = PasswordField('تأكيد كلمة المرور الجديدة',
                                validators=[DataRequired(), EqualTo('new_password')],
                                render_kw={"placeholder": "أعد إدخال كلمة المرور الجديدة"})
    submit = SubmitField('تغيير كلمة المرور')

class ResetPasswordRequestForm(FlaskForm):
    """نموذج طلب إعادة تعيين كلمة المرور"""
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()],
                       render_kw={"placeholder": "أدخل بريدك الإلكتروني"})
    submit = SubmitField('إرسال رابط إعادة التعيين')

class ResetPasswordForm(FlaskForm):
    """نموذج إعادة تعيين كلمة المرور"""
    password = PasswordField('كلمة المرور الجديدة', 
                           validators=[DataRequired(), Length(min=6)],
                           render_kw={"placeholder": "أدخل كلمة المرور الجديدة"})
    password2 = PasswordField('تأكيد كلمة المرور',
                            validators=[DataRequired(), EqualTo('password')],
                            render_kw={"placeholder": "أعد إدخال كلمة المرور"})
    submit = SubmitField('تعيين كلمة المرور')
