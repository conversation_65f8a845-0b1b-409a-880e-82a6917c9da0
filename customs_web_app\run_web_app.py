#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل سريع لتطبيق الويب
Quick start file for web application
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} متوفر")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        return False

def setup_environment():
    """إعداد البيئة"""
    print("⚙️ إعداد البيئة...")
    
    # إنشاء ملف .env إذا لم يكن موجوداً
    if not Path('.env').exists():
        if Path('.env.example').exists():
            import shutil
            shutil.copy('.env.example', '.env')
            print("✅ تم إنشاء ملف .env")
        else:
            # إنشاء ملف .env أساسي
            with open('.env', 'w', encoding='utf-8') as f:
                f.write("""FLASK_CONFIG=development
FLASK_ENV=development
FLASK_DEBUG=1
SECRET_KEY=dev-secret-key-change-in-production
DEV_DATABASE_URL=sqlite:///customs_dev.db
""")
            print("✅ تم إنشاء ملف .env أساسي")
    
    # إنشاء مجلدات مطلوبة
    os.makedirs('app/static/uploads', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    print("✅ تم إنشاء المجلدات المطلوبة")

def initialize_database():
    """تهيئة قاعدة البيانات"""
    print("🗄️ تهيئة قاعدة البيانات...")
    
    try:
        # تهيئة قاعدة البيانات
        subprocess.check_call([sys.executable, '-c', """
import os
os.environ['FLASK_CONFIG'] = 'development'
from app import create_app, db
app = create_app()
with app.app_context():
    db.create_all()
    print("تم إنشاء قاعدة البيانات")
"""])
        
        # إدراج البيانات الأولية
        subprocess.check_call([sys.executable, '-c', """
import os
os.environ['FLASK_CONFIG'] = 'development'
from app import create_app, db
from app.models import User, Role, Permission, Wilaya, Corps, Directorate

app = create_app()
with app.app_context():
    # إنشاء مستخدم مدير
    if not User.query.filter_by(username='admin').first():
        admin = User(
            username='admin',
            email='<EMAIL>',
            first_name='مدير',
            last_name='النظام',
            is_admin=True
        )
        admin.set_password('admin123')
        db.session.add(admin)
        
        # إضافة بعض الولايات
        wilayas = [
            ('01', 'أدرار', 'Adrar'),
            ('16', 'الجزائر', 'Alger'),
            ('31', 'وهران', 'Oran'),
            ('25', 'قسنطينة', 'Constantine')
        ]
        
        for code, name_ar, name_fr in wilayas:
            if not Wilaya.query.filter_by(code=code).first():
                wilaya = Wilaya(code=code, name_ar=name_ar, name_fr=name_fr)
                db.session.add(wilaya)
        
        # إضافة بعض الأسلاك
        corps_data = [
            ('سلك المفتشين الجمركيين', 'Corps des Inspecteurs des Douanes'),
            ('سلك المراقبين الجمركيين', 'Corps des Contrôleurs des Douanes'),
            ('سلك أعوان الجمارك', 'Corps des Agents des Douanes')
        ]
        
        for name_ar, name_fr in corps_data:
            if not Corps.query.filter_by(name_ar=name_ar).first():
                corps = Corps(name_ar=name_ar, name_fr=name_fr)
                db.session.add(corps)
        
        db.session.commit()
        print("تم إدراج البيانات الأولية")
"""])
        
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تهيئة قاعدة البيانات: {e}")
        return False

def run_application():
    """تشغيل التطبيق"""
    print("🚀 تشغيل التطبيق...")
    print("=" * 60)
    print("🌐 تطبيق الويب لتسيير مستخدمي الجمارك الجزائرية")
    print("🌐 Algerian Customs Personnel Management Web App")
    print("=" * 60)
    print()
    print("📍 الرابط: http://localhost:5000")
    print("👤 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print()
    print("⚠️  تذكر تغيير كلمة المرور بعد أول تسجيل دخول!")
    print()
    print("🛑 لإيقاف التطبيق: اضغط Ctrl+C")
    print("=" * 60)
    
    try:
        # تشغيل التطبيق
        os.environ['FLASK_CONFIG'] = 'development'
        subprocess.run([sys.executable, 'app.py'])
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف التطبيق")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 إعداد وتشغيل تطبيق الويب")
    print("=" * 50)
    
    # التحقق من Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # تثبيت المتطلبات
    if not install_requirements():
        input("اضغط Enter للخروج...")
        return
    
    # إعداد البيئة
    setup_environment()
    
    # تهيئة قاعدة البيانات
    if not initialize_database():
        input("اضغط Enter للخروج...")
        return
    
    print("✅ تم الإعداد بنجاح!")
    print()
    
    # تشغيل التطبيق
    run_application()

if __name__ == "__main__":
    main()
