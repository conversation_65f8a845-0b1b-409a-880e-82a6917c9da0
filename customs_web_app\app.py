#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق الويب لتسيير مستخدمي الجمارك الجزائرية
Algerian Customs Personnel Management Web Application

الملف الرئيسي لتشغيل التطبيق
Main application file
"""

import os
from flask_migrate import Migrate
from app import create_app, db
from app.models import (
    User, Role, Permission, UserRole, RolePermission,
    Employee, Wilaya, Commune, Corps, Rank, Directorate, Service, Position,
    Certificate, Training, Language, EmployeeLanguage, Transfer,
    AnnualLeave, SickLeave, OtherLeave
)

# إنشاء التطبيق
app = create_app(os.getenv('FLASK_CONFIG') or 'default')
migrate = Migrate(app, db)

@app.shell_context_processor
def make_shell_context():
    """إضافة المتغيرات إلى shell context"""
    return {
        'db': db,
        'User': User,
        'Role': Role,
        'Permission': Permission,
        'UserRole': UserRole,
        'RolePermission': RolePermission,
        'Employee': Employee,
        'Wilaya': Wilaya,
        'Commune': Commune,
        'Corps': Corps,
        'Rank': Rank,
        'Directorate': Directorate,
        'Service': Service,
        'Position': Position,
        'Certificate': Certificate,
        'Training': Training,
        'Language': Language,
        'EmployeeLanguage': EmployeeLanguage,
        'Transfer': Transfer,
        'AnnualLeave': AnnualLeave,
        'SickLeave': SickLeave,
        'OtherLeave': OtherLeave
    }

@app.cli.command()
def init_db():
    """تهيئة قاعدة البيانات"""
    print("إنشاء قاعدة البيانات...")
    db.create_all()
    print("✅ تم إنشاء قاعدة البيانات بنجاح")

@app.cli.command()
def seed_db():
    """إدراج البيانات الأولية"""
    print("إدراج البيانات الأولية...")
    
    # إنشاء الصلاحيات
    permissions = [
        ('view_employees', 'عرض الموظفين', 'employees'),
        ('add_employees', 'إضافة موظفين', 'employees'),
        ('edit_employees', 'تعديل الموظفين', 'employees'),
        ('delete_employees', 'حذف الموظفين', 'employees'),
        ('view_reports', 'عرض التقارير', 'reports'),
        ('generate_reports', 'إنتاج التقارير', 'reports'),
        ('view_settings', 'عرض الإعدادات', 'settings'),
        ('edit_settings', 'تعديل الإعدادات', 'settings'),
        ('manage_users', 'إدارة المستخدمين', 'admin'),
        ('system_admin', 'إدارة النظام', 'admin')
    ]
    
    for name, desc, category in permissions:
        if not Permission.query.filter_by(name=name).first():
            permission = Permission(name=name, description=desc, category=category)
            db.session.add(permission)
    
    # إنشاء الأدوار
    roles = [
        ('admin', 'مدير النظام'),
        ('hr_manager', 'مدير الموارد البشرية'),
        ('hr_employee', 'موظف الموارد البشرية'),
        ('viewer', 'مستخدم عادي')
    ]
    
    for name, desc in roles:
        if not Role.query.filter_by(name=name).first():
            role = Role(name=name, description=desc)
            db.session.add(role)
    
    db.session.commit()
    
    # ربط الصلاحيات بالأدوار
    admin_role = Role.query.filter_by(name='admin').first()
    if admin_role:
        # المدير له جميع الصلاحيات
        all_permissions = Permission.query.all()
        for permission in all_permissions:
            if not RolePermission.query.filter_by(role_id=admin_role.id, permission_id=permission.id).first():
                role_permission = RolePermission(role_id=admin_role.id, permission_id=permission.id)
                db.session.add(role_permission)
    
    # إنشاء المستخدم الافتراضي
    if not User.query.filter_by(username='admin').first():
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            first_name='مدير',
            last_name='النظام',
            is_admin=True
        )
        admin_user.set_password('admin123')
        db.session.add(admin_user)
        db.session.commit()
        
        # ربط المستخدم بدور المدير
        if admin_role:
            user_role = UserRole(user_id=admin_user.id, role_id=admin_role.id)
            db.session.add(user_role)
    
    # إدراج الولايات الجزائرية
    wilayas_data = [
        ('01', 'أدرار', 'Adrar'),
        ('02', 'الشلف', 'Chlef'),
        ('03', 'الأغواط', 'Laghouat'),
        ('04', 'أم البواقي', 'Oum El Bouaghi'),
        ('05', 'باتنة', 'Batna'),
        ('06', 'بجاية', 'Béjaïa'),
        ('07', 'بسكرة', 'Biskra'),
        ('08', 'بشار', 'Béchar'),
        ('09', 'البليدة', 'Blida'),
        ('10', 'البويرة', 'Bouira'),
        ('11', 'تمنراست', 'Tamanrasset'),
        ('12', 'تبسة', 'Tébessa'),
        ('13', 'تلمسان', 'Tlemcen'),
        ('14', 'تيارت', 'Tiaret'),
        ('15', 'تيزي وزو', 'Tizi Ouzou'),
        ('16', 'الجزائر', 'Alger'),
        ('17', 'الجلفة', 'Djelfa'),
        ('18', 'جيجل', 'Jijel'),
        ('19', 'سطيف', 'Sétif'),
        ('20', 'سعيدة', 'Saïda'),
        ('21', 'سكيكدة', 'Skikda'),
        ('22', 'سيدي بلعباس', 'Sidi Bel Abbès'),
        ('23', 'عنابة', 'Annaba'),
        ('24', 'قالمة', 'Guelma'),
        ('25', 'قسنطينة', 'Constantine'),
        ('26', 'المدية', 'Médéa'),
        ('27', 'مستغانم', 'Mostaganem'),
        ('28', 'المسيلة', 'M\'Sila'),
        ('29', 'معسكر', 'Mascara'),
        ('30', 'ورقلة', 'Ouargla'),
        ('31', 'وهران', 'Oran'),
        ('32', 'البيض', 'El Bayadh'),
        ('33', 'إليزي', 'Illizi'),
        ('34', 'برج بوعريريج', 'Bordj Bou Arréridj'),
        ('35', 'بومرداس', 'Boumerdès'),
        ('36', 'الطارف', 'El Tarf'),
        ('37', 'تندوف', 'Tindouf'),
        ('38', 'تيسمسيلت', 'Tissemsilt'),
        ('39', 'الوادي', 'El Oued'),
        ('40', 'خنشلة', 'Khenchela'),
        ('41', 'سوق أهراس', 'Souk Ahras'),
        ('42', 'تيبازة', 'Tipaza'),
        ('43', 'ميلة', 'Mila'),
        ('44', 'عين الدفلى', 'Aïn Defla'),
        ('45', 'النعامة', 'Naâma'),
        ('46', 'عين تموشنت', 'Aïn Témouchent'),
        ('47', 'غرداية', 'Ghardaïa'),
        ('48', 'غليزان', 'Relizane'),
        ('49', 'تيميمون', 'Timimoun'),
        ('50', 'برج باجي مختار', 'Bordj Badji Mokhtar'),
        ('51', 'أولاد جلال', 'Ouled Djellal'),
        ('52', 'بني عباس', 'Béni Abbès'),
        ('53', 'عين صالح', 'In Salah'),
        ('54', 'عين قزام', 'In Guezzam'),
        ('55', 'تقرت', 'Touggourt'),
        ('56', 'جانت', 'Djanet'),
        ('57', 'المغير', 'El M\'Ghair'),
        ('58', 'المنيعة', 'El Meniaa')
    ]
    
    for code, name_ar, name_fr in wilayas_data:
        if not Wilaya.query.filter_by(code=code).first():
            wilaya = Wilaya(code=code, name_ar=name_ar, name_fr=name_fr)
            db.session.add(wilaya)
    
    # إدراج الأسلاك الجمركية
    corps_data = [
        ('سلك المفتشين الجمركيين', 'Corps des Inspecteurs des Douanes'),
        ('سلك المراقبين الجمركيين', 'Corps des Contrôleurs des Douanes'),
        ('سلك الأعوان التقنيين للجمارك', 'Corps des Agents Techniques des Douanes'),
        ('سلك أعوان الجمارك', 'Corps des Agents des Douanes'),
        ('سلك الإدارة العامة', 'Corps de l\'Administration Générale')
    ]
    
    for name_ar, name_fr in corps_data:
        if not Corps.query.filter_by(name_ar=name_ar).first():
            corps = Corps(name_ar=name_ar, name_fr=name_fr)
            db.session.add(corps)
    
    # إدراج المديريات
    directorates_data = [
        ('المديرية العامة للجمارك', 'Direction Générale des Douanes'),
        ('مديرية الموارد البشرية', 'Direction des Ressources Humaines'),
        ('مديرية العمليات الجمركية', 'Direction des Opérations Douanières'),
        ('مديرية التحقيقات الجمركية', 'Direction des Enquêtes Douanières'),
        ('مديرية المعلوماتية', 'Direction de l\'Informatique'),
        ('مديرية المالية والمحاسبة', 'Direction des Finances et de la Comptabilité')
    ]
    
    for name_ar, name_fr in directorates_data:
        if not Directorate.query.filter_by(name_ar=name_ar).first():
            directorate = Directorate(name_ar=name_ar, name_fr=name_fr)
            db.session.add(directorate)
    
    db.session.commit()
    print("✅ تم إدراج البيانات الأولية بنجاح")

@app.cli.command()
def create_admin():
    """إنشاء مستخدم مدير جديد"""
    username = input("اسم المستخدم: ")
    email = input("البريد الإلكتروني: ")
    first_name = input("الاسم الأول: ")
    last_name = input("اسم العائلة: ")
    password = input("كلمة المرور: ")
    
    if User.query.filter_by(username=username).first():
        print("❌ اسم المستخدم موجود بالفعل")
        return
    
    if User.query.filter_by(email=email).first():
        print("❌ البريد الإلكتروني موجود بالفعل")
        return
    
    user = User(
        username=username,
        email=email,
        first_name=first_name,
        last_name=last_name,
        is_admin=True
    )
    user.set_password(password)
    
    db.session.add(user)
    db.session.commit()
    
    print(f"✅ تم إنشاء المستخدم المدير {username} بنجاح")

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
