# -*- coding: utf-8 -*-
"""
واجهة إدارة الإعدادات
Settings Management Interface
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import openpyxl
from openpyxl import Workbook

from database.models import Wilaya, Commune, Corps, Rank, Directorate, Service, Position, PenaltyType

class SettingsManagementFrame(ttk.Frame):
    """إطار إدارة الإعدادات"""
    
    def __init__(self, parent, db_manager):
        super().__init__(parent)
        self.db_manager = db_manager
        
        # إنشاء الواجهة
        self.create_widgets()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إنشاء Notebook للتبويبات
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # تبويب التقسيم الإداري
        self.create_administrative_division_tab()
        
        # تبويب الأسلاك والرتب
        self.create_corps_ranks_tab()
        
        # تبويب المديريات والمصالح
        self.create_directorates_services_tab()
        
        # تبويب الوظائف
        self.create_positions_tab()
        
        # تبويب أنواع العقوبات
        self.create_penalty_types_tab()
    
    def create_administrative_division_tab(self):
        """إنشاء تبويب التقسيم الإداري"""
        self.admin_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.admin_frame, text="التقسيم الإداري")
        
        # إطار الأزرار
        buttons_frame = ttk.Frame(self.admin_frame)
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(
            buttons_frame,
            text="استيراد من Excel",
            command=self.import_administrative_division
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            buttons_frame,
            text="تصدير إلى Excel",
            command=self.export_administrative_division
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            buttons_frame,
            text="إضافة ولاية",
            command=self.add_wilaya
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            buttons_frame,
            text="إضافة بلدية",
            command=self.add_commune
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            buttons_frame,
            text="تحديث",
            command=self.load_administrative_division
        ).pack(side=tk.LEFT, padx=5)
        
        # إطار الولايات والبلديات
        main_frame = ttk.Frame(self.admin_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # قائمة الولايات
        wilayas_frame = ttk.LabelFrame(main_frame, text="الولايات", padding=5)
        wilayas_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        self.wilayas_tree = ttk.Treeview(
            wilayas_frame,
            columns=("code", "name_ar", "name_fr"),
            show='headings',
            height=15
        )
        
        self.wilayas_tree.heading("code", text="الرمز")
        self.wilayas_tree.heading("name_ar", text="الاسم بالعربية")
        self.wilayas_tree.heading("name_fr", text="الاسم بالفرنسية")
        
        self.wilayas_tree.column("code", width=60)
        self.wilayas_tree.column("name_ar", width=150)
        self.wilayas_tree.column("name_fr", width=150)
        
        wilayas_scrollbar = ttk.Scrollbar(wilayas_frame, orient=tk.VERTICAL, command=self.wilayas_tree.yview)
        self.wilayas_tree.configure(yscrollcommand=wilayas_scrollbar.set)
        
        self.wilayas_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        wilayas_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.wilayas_tree.bind('<<TreeviewSelect>>', self.on_wilaya_select)
        
        # قائمة البلديات
        communes_frame = ttk.LabelFrame(main_frame, text="البلديات", padding=5)
        communes_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5)
        
        self.communes_tree = ttk.Treeview(
            communes_frame,
            columns=("code", "name_ar", "name_fr"),
            show='headings',
            height=15
        )
        
        self.communes_tree.heading("code", text="الرمز")
        self.communes_tree.heading("name_ar", text="الاسم بالعربية")
        self.communes_tree.heading("name_fr", text="الاسم بالفرنسية")
        
        self.communes_tree.column("code", width=80)
        self.communes_tree.column("name_ar", width=150)
        self.communes_tree.column("name_fr", width=150)
        
        communes_scrollbar = ttk.Scrollbar(communes_frame, orient=tk.VERTICAL, command=self.communes_tree.yview)
        self.communes_tree.configure(yscrollcommand=communes_scrollbar.set)
        
        self.communes_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        communes_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # تحميل البيانات
        self.load_administrative_division()
    
    def create_corps_ranks_tab(self):
        """إنشاء تبويب الأسلاك والرتب"""
        self.corps_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.corps_frame, text="الأسلاك والرتب")
        
        # إطار الأزرار
        buttons_frame = ttk.Frame(self.corps_frame)
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(
            buttons_frame,
            text="إضافة سلك",
            command=self.add_corps
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            buttons_frame,
            text="إضافة رتبة",
            command=self.add_rank
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            buttons_frame,
            text="تحديث",
            command=self.load_corps_ranks
        ).pack(side=tk.LEFT, padx=5)
        
        # إطار الأسلاك والرتب
        main_frame = ttk.Frame(self.corps_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # قائمة الأسلاك
        corps_frame = ttk.LabelFrame(main_frame, text="الأسلاك", padding=5)
        corps_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        self.corps_tree = ttk.Treeview(
            corps_frame,
            columns=("name_ar", "name_fr"),
            show='headings',
            height=15
        )
        
        self.corps_tree.heading("name_ar", text="الاسم بالعربية")
        self.corps_tree.heading("name_fr", text="الاسم بالفرنسية")
        
        self.corps_tree.column("name_ar", width=200)
        self.corps_tree.column("name_fr", width=200)
        
        corps_scrollbar = ttk.Scrollbar(corps_frame, orient=tk.VERTICAL, command=self.corps_tree.yview)
        self.corps_tree.configure(yscrollcommand=corps_scrollbar.set)
        
        self.corps_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        corps_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.corps_tree.bind('<<TreeviewSelect>>', self.on_corps_select)
        
        # قائمة الرتب
        ranks_frame = ttk.LabelFrame(main_frame, text="الرتب", padding=5)
        ranks_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5)
        
        self.ranks_tree = ttk.Treeview(
            ranks_frame,
            columns=("name_ar", "name_fr", "level"),
            show='headings',
            height=15
        )
        
        self.ranks_tree.heading("name_ar", text="الاسم بالعربية")
        self.ranks_tree.heading("name_fr", text="الاسم بالفرنسية")
        self.ranks_tree.heading("level", text="المستوى")
        
        self.ranks_tree.column("name_ar", width=200)
        self.ranks_tree.column("name_fr", width=200)
        self.ranks_tree.column("level", width=80)
        
        ranks_scrollbar = ttk.Scrollbar(ranks_frame, orient=tk.VERTICAL, command=self.ranks_tree.yview)
        self.ranks_tree.configure(yscrollcommand=ranks_scrollbar.set)
        
        self.ranks_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        ranks_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # تحميل البيانات
        self.load_corps_ranks()
    
    def create_directorates_services_tab(self):
        """إنشاء تبويب المديريات والمصالح"""
        self.directorates_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.directorates_frame, text="المديريات والمصالح")
        
        # إطار الأزرار
        buttons_frame = ttk.Frame(self.directorates_frame)
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(
            buttons_frame,
            text="إضافة مديرية",
            command=self.add_directorate
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            buttons_frame,
            text="إضافة مصلحة",
            command=self.add_service
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            buttons_frame,
            text="تحديث",
            command=self.load_directorates_services
        ).pack(side=tk.LEFT, padx=5)
        
        # شجرة المديريات والمصالح
        tree_frame = ttk.Frame(self.directorates_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.directorates_tree = ttk.Treeview(
            tree_frame,
            columns=("name_ar", "name_fr", "code"),
            show='tree headings',
            height=20
        )
        
        self.directorates_tree.heading("#0", text="الهيكل")
        self.directorates_tree.heading("name_ar", text="الاسم بالعربية")
        self.directorates_tree.heading("name_fr", text="الاسم بالفرنسية")
        self.directorates_tree.heading("code", text="الرمز")
        
        self.directorates_tree.column("#0", width=200)
        self.directorates_tree.column("name_ar", width=250)
        self.directorates_tree.column("name_fr", width=250)
        self.directorates_tree.column("code", width=100)
        
        directorates_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.directorates_tree.yview)
        self.directorates_tree.configure(yscrollcommand=directorates_scrollbar.set)
        
        self.directorates_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        directorates_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # تحميل البيانات
        self.load_directorates_services()
    
    def create_positions_tab(self):
        """إنشاء تبويب الوظائف"""
        self.positions_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.positions_frame, text="الوظائف")
        
        # إطار الأزرار
        buttons_frame = ttk.Frame(self.positions_frame)
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(
            buttons_frame,
            text="إضافة وظيفة",
            command=self.add_position
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            buttons_frame,
            text="تحديث",
            command=self.load_positions
        ).pack(side=tk.LEFT, padx=5)
        
        # جدول الوظائف
        table_frame = ttk.Frame(self.positions_frame)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.positions_tree = ttk.Treeview(
            table_frame,
            columns=("name_ar", "name_fr", "service", "description"),
            show='headings',
            height=20
        )
        
        self.positions_tree.heading("name_ar", text="الاسم بالعربية")
        self.positions_tree.heading("name_fr", text="الاسم بالفرنسية")
        self.positions_tree.heading("service", text="المصلحة")
        self.positions_tree.heading("description", text="الوصف")
        
        self.positions_tree.column("name_ar", width=200)
        self.positions_tree.column("name_fr", width=200)
        self.positions_tree.column("service", width=200)
        self.positions_tree.column("description", width=300)
        
        positions_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.positions_tree.yview)
        self.positions_tree.configure(yscrollcommand=positions_scrollbar.set)
        
        self.positions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        positions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # تحميل البيانات
        self.load_positions()
    
    def create_penalty_types_tab(self):
        """إنشاء تبويب أنواع العقوبات"""
        self.penalty_types_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.penalty_types_frame, text="أنواع العقوبات")
        
        # إطار الأزرار
        buttons_frame = ttk.Frame(self.penalty_types_frame)
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(
            buttons_frame,
            text="إضافة نوع عقوبة",
            command=self.add_penalty_type
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            buttons_frame,
            text="تحديث",
            command=self.load_penalty_types
        ).pack(side=tk.LEFT, padx=5)
        
        # جدول أنواع العقوبات
        table_frame = ttk.Frame(self.penalty_types_frame)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.penalty_types_tree = ttk.Treeview(
            table_frame,
            columns=("degree", "name_ar", "description"),
            show='headings',
            height=20
        )
        
        self.penalty_types_tree.heading("degree", text="الدرجة")
        self.penalty_types_tree.heading("name_ar", text="نوع العقوبة")
        self.penalty_types_tree.heading("description", text="الوصف")
        
        self.penalty_types_tree.column("degree", width=100)
        self.penalty_types_tree.column("name_ar", width=300)
        self.penalty_types_tree.column("description", width=400)
        
        penalty_types_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.penalty_types_tree.yview)
        self.penalty_types_tree.configure(yscrollcommand=penalty_types_scrollbar.set)
        
        self.penalty_types_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        penalty_types_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # تحميل البيانات
        self.load_penalty_types()
    
    # دوال تحميل البيانات
    def load_administrative_division(self):
        """تحميل بيانات التقسيم الإداري"""
        try:
            session = self.db_manager.get_session()
            
            # مسح البيانات الحالية
            for item in self.wilayas_tree.get_children():
                self.wilayas_tree.delete(item)
            
            for item in self.communes_tree.get_children():
                self.communes_tree.delete(item)
            
            # تحميل الولايات
            wilayas = session.query(Wilaya).order_by(Wilaya.code).all()
            for wilaya in wilayas:
                self.wilayas_tree.insert('', 'end', values=(
                    wilaya.code,
                    wilaya.name_ar,
                    wilaya.name_fr or ""
                ), tags=(wilaya.id,))
            
            session.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل بيانات التقسيم الإداري:\n{str(e)}")
    
    def load_corps_ranks(self):
        """تحميل بيانات الأسلاك والرتب"""
        try:
            session = self.db_manager.get_session()
            
            # مسح البيانات الحالية
            for item in self.corps_tree.get_children():
                self.corps_tree.delete(item)
            
            for item in self.ranks_tree.get_children():
                self.ranks_tree.delete(item)
            
            # تحميل الأسلاك
            corps = session.query(Corps).order_by(Corps.name_ar).all()
            for corp in corps:
                self.corps_tree.insert('', 'end', values=(
                    corp.name_ar,
                    corp.name_fr or ""
                ), tags=(corp.id,))
            
            session.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل بيانات الأسلاك والرتب:\n{str(e)}")
    
    def load_directorates_services(self):
        """تحميل بيانات المديريات والمصالح"""
        try:
            session = self.db_manager.get_session()
            
            # مسح البيانات الحالية
            for item in self.directorates_tree.get_children():
                self.directorates_tree.delete(item)
            
            # تحميل المديريات الرئيسية
            directorates = session.query(Directorate).filter_by(parent_id=None).order_by(Directorate.name_ar).all()
            for directorate in directorates:
                parent_item = self.directorates_tree.insert('', 'end', text=directorate.name_ar, values=(
                    directorate.name_ar,
                    directorate.name_fr or "",
                    directorate.code or ""
                ), tags=(directorate.id,))
                
                # إضافة المديريات الفرعية
                self.load_sub_directorates(session, directorate.id, parent_item)
                
                # إضافة المصالح
                services = session.query(Service).filter_by(directorate_id=directorate.id).order_by(Service.name_ar).all()
                for service in services:
                    self.directorates_tree.insert(parent_item, 'end', text=f"مصلحة: {service.name_ar}", values=(
                        service.name_ar,
                        service.name_fr or "",
                        service.code or ""
                    ), tags=(f"service_{service.id}",))
            
            session.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل بيانات المديريات والمصالح:\n{str(e)}")
    
    def load_sub_directorates(self, session, parent_id, parent_item):
        """تحميل المديريات الفرعية"""
        sub_directorates = session.query(Directorate).filter_by(parent_id=parent_id).order_by(Directorate.name_ar).all()
        for sub_directorate in sub_directorates:
            sub_item = self.directorates_tree.insert(parent_item, 'end', text=sub_directorate.name_ar, values=(
                sub_directorate.name_ar,
                sub_directorate.name_fr or "",
                sub_directorate.code or ""
            ), tags=(sub_directorate.id,))
            
            # تحميل المديريات الفرعية للمديرية الفرعية (تكرار)
            self.load_sub_directorates(session, sub_directorate.id, sub_item)
    
    def load_positions(self):
        """تحميل بيانات الوظائف"""
        try:
            session = self.db_manager.get_session()
            
            # مسح البيانات الحالية
            for item in self.positions_tree.get_children():
                self.positions_tree.delete(item)
            
            # تحميل الوظائف
            positions = session.query(Position).join(Service).order_by(Position.name_ar).all()
            for position in positions:
                self.positions_tree.insert('', 'end', values=(
                    position.name_ar,
                    position.name_fr or "",
                    position.service.name_ar if position.service else "",
                    position.description or ""
                ), tags=(position.id,))
            
            session.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل بيانات الوظائف:\n{str(e)}")
    
    def load_penalty_types(self):
        """تحميل بيانات أنواع العقوبات"""
        try:
            session = self.db_manager.get_session()
            
            # مسح البيانات الحالية
            for item in self.penalty_types_tree.get_children():
                self.penalty_types_tree.delete(item)
            
            # تحميل أنواع العقوبات
            penalty_types = session.query(PenaltyType).order_by(PenaltyType.degree, PenaltyType.name_ar).all()
            for penalty_type in penalty_types:
                self.penalty_types_tree.insert('', 'end', values=(
                    penalty_type.degree,
                    penalty_type.name_ar,
                    penalty_type.description or ""
                ), tags=(penalty_type.id,))
            
            session.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل بيانات أنواع العقوبات:\n{str(e)}")
    
    # دوال الأحداث
    def on_wilaya_select(self, event):
        """عند اختيار ولاية"""
        selected_item = self.wilayas_tree.selection()
        if not selected_item:
            return
        
        try:
            wilaya_id = self.wilayas_tree.item(selected_item[0])['tags'][0]
            
            # مسح البلديات الحالية
            for item in self.communes_tree.get_children():
                self.communes_tree.delete(item)
            
            # تحميل بلديات الولاية المختارة
            session = self.db_manager.get_session()
            communes = session.query(Commune).filter_by(wilaya_id=wilaya_id).order_by(Commune.name_ar).all()
            
            for commune in communes:
                self.communes_tree.insert('', 'end', values=(
                    commune.code,
                    commune.name_ar,
                    commune.name_fr or ""
                ), tags=(commune.id,))
            
            session.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البلديات:\n{str(e)}")
    
    def on_corps_select(self, event):
        """عند اختيار سلك"""
        selected_item = self.corps_tree.selection()
        if not selected_item:
            return
        
        try:
            corps_id = self.corps_tree.item(selected_item[0])['tags'][0]
            
            # مسح الرتب الحالية
            for item in self.ranks_tree.get_children():
                self.ranks_tree.delete(item)
            
            # تحميل رتب السلك المختار
            session = self.db_manager.get_session()
            ranks = session.query(Rank).filter_by(corps_id=corps_id).order_by(Rank.level).all()
            
            for rank in ranks:
                self.ranks_tree.insert('', 'end', values=(
                    rank.name_ar,
                    rank.name_fr or "",
                    rank.level or ""
                ), tags=(rank.id,))
            
            session.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الرتب:\n{str(e)}")
    
    # دوال الإضافة والتعديل
    def add_wilaya(self):
        """إضافة ولاية جديدة"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def add_commune(self):
        """إضافة بلدية جديدة"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def add_corps(self):
        """إضافة سلك جديد"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def add_rank(self):
        """إضافة رتبة جديدة"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def add_directorate(self):
        """إضافة مديرية جديدة"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def add_service(self):
        """إضافة مصلحة جديدة"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def add_position(self):
        """إضافة وظيفة جديدة"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def add_penalty_type(self):
        """إضافة نوع عقوبة جديد"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    # دوال الاستيراد والتصدير
    def import_administrative_division(self):
        """استيراد التقسيم الإداري من Excel"""
        file_path = filedialog.askopenfilename(
            title="اختيار ملف Excel",
            filetypes=[("ملفات Excel", "*.xlsx *.xls"), ("جميع الملفات", "*.*")]
        )
        
        if file_path:
            try:
                workbook = openpyxl.load_workbook(file_path)
                
                # استيراد الولايات
                if "الولايات" in workbook.sheetnames:
                    self.import_wilayas_from_excel(workbook["الولايات"])
                
                # استيراد البلديات
                if "البلديات" in workbook.sheetnames:
                    self.import_communes_from_excel(workbook["البلديات"])
                
                messagebox.showinfo("نجح", "تم استيراد البيانات بنجاح")
                self.load_administrative_division()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في استيراد البيانات:\n{str(e)}")
    
    def import_wilayas_from_excel(self, worksheet):
        """استيراد الولايات من ورقة Excel"""
        session = self.db_manager.get_session()
        
        try:
            for row in worksheet.iter_rows(min_row=2, values_only=True):
                if row[0]:  # إذا كان هناك رمز ولاية
                    code = str(row[0]).zfill(2)
                    name_ar = row[1]
                    name_fr = row[2] if len(row) > 2 else None
                    
                    # التحقق من وجود الولاية
                    existing_wilaya = session.query(Wilaya).filter_by(code=code).first()
                    if not existing_wilaya:
                        wilaya = Wilaya(code=code, name_ar=name_ar, name_fr=name_fr)
                        session.add(wilaya)
            
            session.commit()
            
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
    
    def import_communes_from_excel(self, worksheet):
        """استيراد البلديات من ورقة Excel"""
        session = self.db_manager.get_session()
        
        try:
            for row in worksheet.iter_rows(min_row=2, values_only=True):
                if row[0]:  # إذا كان هناك رمز بلدية
                    code = str(row[0])
                    name_ar = row[1]
                    name_fr = row[2] if len(row) > 2 else None
                    wilaya_code = str(row[3]).zfill(2) if len(row) > 3 else None
                    
                    if wilaya_code:
                        wilaya = session.query(Wilaya).filter_by(code=wilaya_code).first()
                        if wilaya:
                            # التحقق من وجود البلدية
                            existing_commune = session.query(Commune).filter_by(code=code).first()
                            if not existing_commune:
                                commune = Commune(
                                    code=code,
                                    name_ar=name_ar,
                                    name_fr=name_fr,
                                    wilaya_id=wilaya.id
                                )
                                session.add(commune)
            
            session.commit()
            
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
    
    def export_administrative_division(self):
        """تصدير التقسيم الإداري إلى Excel"""
        file_path = filedialog.asksaveasfilename(
            title="حفظ ملف Excel",
            defaultextension=".xlsx",
            filetypes=[("ملفات Excel", "*.xlsx"), ("جميع الملفات", "*.*")]
        )
        
        if file_path:
            try:
                workbook = Workbook()
                
                # تصدير الولايات
                wilayas_sheet = workbook.active
                wilayas_sheet.title = "الولايات"
                wilayas_sheet.append(["الرمز", "الاسم بالعربية", "الاسم بالفرنسية"])
                
                session = self.db_manager.get_session()
                wilayas = session.query(Wilaya).order_by(Wilaya.code).all()
                for wilaya in wilayas:
                    wilayas_sheet.append([wilaya.code, wilaya.name_ar, wilaya.name_fr or ""])
                
                # تصدير البلديات
                communes_sheet = workbook.create_sheet("البلديات")
                communes_sheet.append(["الرمز", "الاسم بالعربية", "الاسم بالفرنسية", "رمز الولاية"])
                
                communes = session.query(Commune).join(Wilaya).order_by(Wilaya.code, Commune.name_ar).all()
                for commune in communes:
                    communes_sheet.append([
                        commune.code,
                        commune.name_ar,
                        commune.name_fr or "",
                        commune.wilaya.code
                    ])
                
                session.close()
                
                workbook.save(file_path)
                messagebox.showinfo("نجح", "تم تصدير البيانات بنجاح")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في تصدير البيانات:\n{str(e)}")
