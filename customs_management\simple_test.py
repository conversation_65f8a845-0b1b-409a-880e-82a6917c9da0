#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للبرنامج
"""

import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_tkinter():
    """اختبار tkinter"""
    try:
        import tkinter as tk
        from tkinter import messagebox
        
        print("✅ tkinter imported successfully")
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار البرنامج")
        root.geometry("400x300")
        
        # إضافة نص
        label = tk.Label(root, text="برنامج تسيير مستخدمي الجمارك الجزائرية", 
                        font=("Arial", 14), fg="blue")
        label.pack(pady=20)
        
        label2 = tk.Label(root, text="اختبار النافذة - البرنامج يعمل بنجاح!", 
                         font=("Arial", 12))
        label2.pack(pady=10)
        
        # زر الإغلاق
        def close_app():
            root.destroy()
            
        button = tk.Button(root, text="إغلاق", command=close_app, 
                          font=("Arial", 12), bg="lightblue")
        button.pack(pady=20)
        
        # زر اختبار قاعدة البيانات
        def test_db():
            try:
                from database.database_manager import DatabaseManager
                db_manager = DatabaseManager()
                db_manager.create_tables()
                messagebox.showinfo("نجح", "تم إنشاء قاعدة البيانات بنجاح!")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في قاعدة البيانات:\n{e}")
        
        db_button = tk.Button(root, text="اختبار قاعدة البيانات", command=test_db,
                             font=("Arial", 10), bg="lightgreen")
        db_button.pack(pady=10)
        
        print("🚀 نافذة الاختبار جاهزة!")
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في tkinter: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار بسيط للبرنامج...")
    print("=" * 40)
    
    if test_tkinter():
        print("✅ الاختبار نجح!")
    else:
        print("❌ الاختبار فشل!")

if __name__ == "__main__":
    main()
