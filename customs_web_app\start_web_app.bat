@echo off
chcp 65001 > nul
title تطبيق الويب - نظام إدارة موظفي الجمارك

echo ========================================
echo 🌐 تطبيق الويب لتسيير مستخدمي الجمارك
echo 🌐 Customs Web Application
echo ========================================
echo.

echo 🔍 التحقق من Python...
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

echo.
echo 📦 تثبيت المتطلبات...
pip install -r requirements.txt > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المتطلبات
    echo جاري المحاولة مرة أخرى...
    pip install --upgrade pip
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المتطلبات نهائياً
        pause
        exit /b 1
    )
)

echo ✅ تم تثبيت المتطلبات بنجاح

echo.
echo ⚙️ إعداد البيئة...
if not exist .env (
    if exist .env.example (
        copy .env.example .env > nul
        echo ✅ تم إنشاء ملف .env
    ) else (
        echo FLASK_CONFIG=development > .env
        echo FLASK_ENV=development >> .env
        echo FLASK_DEBUG=1 >> .env
        echo SECRET_KEY=dev-secret-key-change-in-production >> .env
        echo DEV_DATABASE_URL=sqlite:///customs_dev.db >> .env
        echo ✅ تم إنشاء ملف .env أساسي
    )
)

if not exist "app\static\uploads" mkdir "app\static\uploads"
if not exist "logs" mkdir "logs"
echo ✅ تم إنشاء المجلدات المطلوبة

echo.
echo 🗄️ تهيئة قاعدة البيانات...
python run_web_app.py > nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ تشغيل الإعداد التفاعلي...
    python run_web_app.py
) else (
    echo ✅ تم إعداد قاعدة البيانات
    
    echo.
    echo ========================================
    echo 🎉 التطبيق جاهز للتشغيل!
    echo ========================================
    echo.
    echo 📍 الرابط: http://localhost:5000
    echo 👤 اسم المستخدم: admin
    echo 🔑 كلمة المرور: admin123
    echo.
    echo ⚠️ تذكر تغيير كلمة المرور بعد أول تسجيل دخول!
    echo.
    echo 🚀 جاري تشغيل التطبيق...
    echo 🛑 لإيقاف التطبيق: اضغط Ctrl+C
    echo ========================================
    echo.
    
    set FLASK_CONFIG=development
    python app.py
)

echo.
echo تم إغلاق التطبيق
pause
