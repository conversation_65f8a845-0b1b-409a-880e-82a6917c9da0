{% extends "base.html" %}

{% block content %}
<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- الجانب الأيسر - معلومات النظام -->
        <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center" 
             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="text-center text-white">
                <div class="mb-4">
                    <i class="fas fa-building fa-5x mb-3"></i>
                    <h2 class="fw-bold">نظام إدارة موظفي الجمارك</h2>
                    <p class="lead">الجمارك الجزائرية</p>
                </div>
                
                <div class="row text-center mt-5">
                    <div class="col-4">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h5>إدارة الموظفين</h5>
                        <p class="small">إدارة شاملة لبيانات الموظفين</p>
                    </div>
                    <div class="col-4">
                        <i class="fas fa-chart-bar fa-2x mb-2"></i>
                        <h5>التقارير</h5>
                        <p class="small">تقارير مفصلة وإحصائيات</p>
                    </div>
                    <div class="col-4">
                        <i class="fas fa-shield-alt fa-2x mb-2"></i>
                        <h5>الأمان</h5>
                        <p class="small">حماية عالية للبيانات</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الجانب الأيمن - نموذج تسجيل الدخول -->
        <div class="col-lg-6 d-flex align-items-center justify-content-center">
            <div class="w-100" style="max-width: 400px;">
                <div class="card shadow-lg border-0">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="fas fa-user-circle fa-3x text-primary mb-3"></i>
                            <h3 class="fw-bold">تسجيل الدخول</h3>
                            <p class="text-muted">أدخل بياناتك للوصول إلى النظام</p>
                        </div>
                        
                        <form method="POST" action="{{ url_for('auth.login') }}">
                            {{ form.hidden_tag() }}
                            
                            <div class="mb-3">
                                <label for="{{ form.username.id }}" class="form-label">
                                    <i class="fas fa-user me-2"></i>{{ form.username.label.text }}
                                </label>
                                {{ form.username(class="form-control form-control-lg") }}
                                {% if form.username.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.username.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.password.id }}" class="form-label">
                                    <i class="fas fa-lock me-2"></i>{{ form.password.label.text }}
                                </label>
                                {{ form.password(class="form-control form-control-lg") }}
                                {% if form.password.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.password.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3 form-check">
                                {{ form.remember_me(class="form-check-input") }}
                                <label class="form-check-label" for="{{ form.remember_me.id }}">
                                    {{ form.remember_me.label.text }}
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                {{ form.submit(class="btn btn-primary btn-lg") }}
                            </div>
                        </form>
                        
                        <div class="text-center mt-4">
                            <a href="#" class="text-decoration-none small">
                                نسيت كلمة المرور؟
                            </a>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <small class="text-muted">
                                للحصول على حساب جديد، يرجى التواصل مع المدير
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات إضافية -->
                <div class="text-center mt-4">
                    <div class="row">
                        <div class="col-6">
                            <div class="card bg-light border-0">
                                <div class="card-body py-3">
                                    <i class="fas fa-clock text-primary mb-2"></i>
                                    <h6 class="mb-0">24/7</h6>
                                    <small class="text-muted">متاح دائماً</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card bg-light border-0">
                                <div class="card-body py-3">
                                    <i class="fas fa-mobile-alt text-primary mb-2"></i>
                                    <h6 class="mb-0">متجاوب</h6>
                                    <small class="text-muted">جميع الأجهزة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer للصفحة -->
<div class="position-fixed bottom-0 start-0 end-0 bg-white border-top py-2">
    <div class="container text-center">
        <small class="text-muted">
            © {{ current_year }} الجمارك الجزائرية - نظام إدارة الموظفين
        </small>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تأثيرات بصرية للنموذج
    $('.form-control').on('focus', function() {
        $(this).parent().addClass('focused');
    }).on('blur', function() {
        if (!$(this).val()) {
            $(this).parent().removeClass('focused');
        }
    });
    
    // تحقق من صحة النموذج
    $('form').on('submit', function(e) {
        let isValid = true;
        
        $('.form-control').each(function() {
            if ($(this).prop('required') && !$(this).val()) {
                isValid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'يرجى ملء جميع الحقول المطلوبة',
                confirmButtonText: 'موافق'
            });
        }
    });
    
    // إضافة تأثير loading عند الإرسال
    $('form').on('submit', function() {
        const submitBtn = $(this).find('input[type="submit"]');
        submitBtn.prop('disabled', true);
        submitBtn.val('جاري تسجيل الدخول...');
        
        // إضافة spinner
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...');
    });
});
</script>
{% endblock %}
