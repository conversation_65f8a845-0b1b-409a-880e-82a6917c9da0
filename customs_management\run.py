#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط للبرنامج
Simple run file for the application
"""

import sys
import os

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    try:
        print("بدء تشغيل برنامج تسيير مستخدمي الجمارك الجزائرية...")
        print("Starting Algerian Customs Personnel Management System...")
        
        # استيراد المكتبات المطلوبة
        import tkinter as tk
        from tkinter import messagebox
        
        print("✓ تم تحميل مكتبة Tkinter بنجاح")
        
        # استيراد وحدات البرنامج
        try:
            from database.database_manager import DatabaseManager
            print("✓ تم تحميل مدير قاعدة البيانات")
        except ImportError as e:
            print(f"❌ خطأ في استيراد مدير قاعدة البيانات: {e}")
            raise

        try:
            from gui.main_window import MainWindow
            print("✓ تم تحميل النافذة الرئيسية")
        except ImportError as e:
            print(f"❌ خطأ في استيراد النافذة الرئيسية: {e}")
            raise

        try:
            from utils.config import Config
            print("✓ تم تحميل الإعدادات")
        except ImportError as e:
            print(f"❌ خطأ في استيراد الإعدادات: {e}")
            raise

        print("✓ تم تحميل وحدات البرنامج بنجاح")
        
        # إنشاء قاعدة البيانات
        print("إنشاء قاعدة البيانات...")
        db_manager = DatabaseManager()
        db_manager.create_tables()
        print("✓ تم إنشاء قاعدة البيانات بنجاح")
        
        # إنشاء النافذة الرئيسية
        print("إنشاء واجهة المستخدم...")
        root = tk.Tk()
        app = MainWindow(root, db_manager)
        print("✓ تم إنشاء واجهة المستخدم بنجاح")
        
        print("🚀 البرنامج جاهز للاستخدام!")
        print("🚀 Application is ready to use!")
        
        # تشغيل التطبيق
        root.mainloop()
        
    except ImportError as e:
        error_msg = f"خطأ في استيراد المكتبات:\n{str(e)}\n\nيرجى التأكد من تثبيت جميع المتطلبات باستخدام:\npip install -r requirements.txt"
        print(f"❌ {error_msg}")
        try:
            messagebox.showerror("خطأ في الاستيراد", error_msg)
        except:
            pass
        sys.exit(1)
        
    except Exception as e:
        error_msg = f"حدث خطأ في تشغيل البرنامج:\n{str(e)}"
        print(f"❌ {error_msg}")
        try:
            messagebox.showerror("خطأ", error_msg)
        except:
            pass
        sys.exit(1)

if __name__ == "__main__":
    main()
