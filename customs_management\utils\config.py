# -*- coding: utf-8 -*-
"""
ملف الإعدادات العامة للبرنامج
Configuration file for the application
"""

import os

class Config:
    """فئة الإعدادات العامة"""
    
    # مسارات الملفات
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    DATABASE_PATH = os.path.join(BASE_DIR, 'data', 'customs_db.sqlite')
    IMAGES_DIR = os.path.join(BASE_DIR, 'data', 'images')
    REPORTS_DIR = os.path.join(BASE_DIR, 'data', 'reports')
    BACKUP_DIR = os.path.join(BASE_DIR, 'data', 'backups')
    
    # إعدادات قاعدة البيانات
    DATABASE_URL = f'sqlite:///{DATABASE_PATH}'
    
    # إعدادات الواجهة
    WINDOW_TITLE = "برنامج تسيير مستخدمي الجمارك الجزائرية"
    WINDOW_SIZE = "1200x800"
    THEME = "clam"
    
    # إعدادات الصور
    MAX_IMAGE_SIZE = (300, 400)  # العرض × الارتفاع
    ALLOWED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.bmp']
    
    # إعدادات العطل
    ANNUAL_LEAVE_DAYS = 50  # عدد أيام العطلة السنوية
    MAX_EXCEPTIONAL_LEAVE_DAYS = 10  # الحد الأقصى للعطل الاستثنائية
    MAX_DEPOSIT_YEARS = 5  # الحد الأقصى لسنوات الاستيداع
    
    # إعدادات التحقق
    MIN_AGE = 19  # الحد الأدنى للعمر
    MAX_AGE = 65  # الحد الأقصى للعمر
    
    # إعدادات التقارير
    REPORT_FORMATS = ['PDF', 'Excel', 'Word']
    
    @classmethod
    def create_directories(cls):
        """إنشاء المجلدات المطلوبة"""
        directories = [
            os.path.dirname(cls.DATABASE_PATH),
            cls.IMAGES_DIR,
            cls.REPORTS_DIR,
            cls.BACKUP_DIR
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
    
    @classmethod
    def get_image_path(cls, employee_id):
        """الحصول على مسار صورة الموظف"""
        return os.path.join(cls.IMAGES_DIR, f"employee_{employee_id}.jpg")
